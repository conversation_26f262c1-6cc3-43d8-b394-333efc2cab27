"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ruler, Info } from "lucide-react";

interface SizeGuideProps {
  productBrand?: string;
  productType?: string;
}

export default function SizeGuide({ productBrand = "General", productType = "Sneakers" }: SizeGuideProps) {
  const [selectedSystem, setSelectedSystem] = useState<"US" | "UK" | "EU">("US");

  // Size conversion chart for sneakers
  const sizeChart = [
    { us: "3", uk: "2.5", eu: "35", cm: "22" },
    { us: "3.5", uk: "3", eu: "35.5", cm: "22.5" },
    { us: "4", uk: "3.5", eu: "36", cm: "23" },
    { us: "4.5", uk: "4", eu: "37", cm: "23.5" },
    { us: "5", uk: "4.5", eu: "37.5", cm: "24" },
    { us: "5.5", uk: "5", eu: "38", cm: "24.5" },
    { us: "6", uk: "5.5", eu: "39", cm: "25" },
    { us: "6.5", uk: "6", eu: "39.5", cm: "25.5" },
    { us: "7", uk: "6.5", eu: "40", cm: "26" },
    { us: "7.5", uk: "7", eu: "41", cm: "26.5" },
    { us: "8", uk: "7.5", eu: "41.5", cm: "27" },
    { us: "8.5", uk: "8", eu: "42", cm: "27.5" },
    { us: "9", uk: "8.5", eu: "43", cm: "28" },
    { us: "9.5", uk: "9", eu: "43.5", cm: "28.5" },
    { us: "10", uk: "9.5", eu: "44", cm: "29" },
    { us: "10.5", uk: "10", eu: "44.5", cm: "29.5" },
    { us: "11", uk: "10.5", eu: "45", cm: "30" },
    { us: "11.5", uk: "11", eu: "45.5", cm: "30.5" },
    { us: "12", uk: "11.5", eu: "46", cm: "31" },
  ];

  const brandSpecificNotes = {
    Nike: "Nike shoes typically run true to size. For a more comfortable fit, consider going up 0.5 size.",
    Adidas: "Adidas shoes generally run true to size, but some models may run slightly large.",
    Vans: "Vans typically run true to size. The classic styles tend to have a snug fit initially.",
    Puma: "Puma shoes usually run true to size. Some performance models may run slightly small.",
    Lacoste: "Lacoste shoes generally run true to size with a comfortable fit.",
    "Air Jordan": "Air Jordan shoes typically run true to size, similar to Nike sizing.",
    "New Balance": "New Balance shoes generally run true to size with good width options."
  };

  const currentNote = brandSpecificNotes[productBrand as keyof typeof brandSpecificNotes] || 
    "Sizing may vary by brand and model. Please refer to the size chart for the best fit.";

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Ruler className="h-4 w-4" />
          Size Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Ruler className="h-5 w-5" />
            Size Guide - {productBrand} {productType}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Size System Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Size System:</span>
            {(["US", "UK", "EU"] as const).map((system) => (
              <Badge
                key={system}
                variant={selectedSystem === system ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedSystem(system)}
              >
                {system}
              </Badge>
            ))}
          </div>

          {/* Brand-Specific Note */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">{productBrand} Sizing Note</h4>
                <p className="text-sm text-blue-800">{currentNote}</p>
              </div>
            </div>
          </div>

          {/* Size Chart */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2 text-left font-medium">US Size</th>
                  <th className="border border-gray-300 px-4 py-2 text-left font-medium">UK Size</th>
                  <th className="border border-gray-300 px-4 py-2 text-left font-medium">EU Size</th>
                  <th className="border border-gray-300 px-4 py-2 text-left font-medium">Length (cm)</th>
                </tr>
              </thead>
              <tbody>
                {sizeChart.map((size, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className={`border border-gray-300 px-4 py-2 ${selectedSystem === "US" ? "bg-blue-50 font-medium" : ""}`}>
                      {size.us}
                    </td>
                    <td className={`border border-gray-300 px-4 py-2 ${selectedSystem === "UK" ? "bg-blue-50 font-medium" : ""}`}>
                      {size.uk}
                    </td>
                    <td className={`border border-gray-300 px-4 py-2 ${selectedSystem === "EU" ? "bg-blue-50 font-medium" : ""}`}>
                      {size.eu}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">{size.cm}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* How to Measure */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium mb-3">How to Measure Your Foot</h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
              <li>Place a piece of paper on the floor against a wall</li>
              <li>Stand on the paper with your heel against the wall</li>
              <li>Mark the longest part of your foot on the paper</li>
              <li>Measure the distance from the wall to the mark in centimeters</li>
              <li>Compare your measurement to the length column in the chart above</li>
              <li>For the best fit, measure both feet and use the larger measurement</li>
            </ol>
          </div>

          {/* Additional Tips */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-900 mb-2">Fitting Tips</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-yellow-800">
              <li>Measure your feet in the evening when they are at their largest</li>
              <li>Wear the type of socks you plan to wear with the shoes</li>
              <li>If you're between sizes, consider the shoe's intended use</li>
              <li>For athletic activities, you may want a slightly larger size</li>
              <li>For casual wear, a snugger fit might be preferred</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
