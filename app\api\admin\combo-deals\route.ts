import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-utils";

// GET /api/admin/combo-deals - Get all combo deals
export async function GET(request: NextRequest) {
  try {
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "all";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status === "active") {
      where.isActive = true;
    } else if (status === "inactive") {
      where.isActive = false;
    } else if (status === "expired") {
      where.validUntil = { lt: new Date() };
    }

    const [combos, total] = await Promise.all([
      prisma.comboDeal.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          comboProducts: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  brand: true,
                  price: true,
                  images: true,
                },
              },
            },
          },
          _count: {
            select: {
              cartItems: true,
              orderItems: true,
            },
          },
        },
      }),
      prisma.comboDeal.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        combos,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching combo deals:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch combo deals" },
      { status: 500 }
    );
  }
}

// POST /api/admin/combo-deals - Create new combo deal
export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin();
    const body = await request.json();

    const {
      name,
      description,
      comboPrice,
      validUntil,
      isActive = true,
      isFeatured = false,
      productIds,
    } = body;

    // Validate required fields
    if (!name || !comboPrice || !validUntil || !productIds || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate that all products exist
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
      select: { id: true, price: true, discountedPrice: true },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { success: false, error: "Some products not found" },
        { status: 400 }
      );
    }

    // Calculate original price and discount
    const originalPrice = products.reduce((sum, product) => {
      return sum + (product.discountedPrice || product.price);
    }, 0);

    const discount = originalPrice - comboPrice;
    const discountPercent = (discount / originalPrice) * 100;

    if (discount <= 0) {
      return NextResponse.json(
        { success: false, error: "Combo price must be less than original price" },
        { status: 400 }
      );
    }

    // Create combo deal with products
    const combo = await prisma.comboDeal.create({
      data: {
        name,
        description,
        originalPrice,
        comboPrice,
        discount,
        discountPercent,
        validUntil: new Date(validUntil),
        isActive,
        isFeatured,
        createdBy: user.id,
        comboProducts: {
          create: productIds.map((productId: string) => ({
            productId,
            quantity: 1,
          })),
        },
      },
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                images: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: combo,
      message: "Combo deal created successfully",
    });
  } catch (error) {
    console.error("Error creating combo deal:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create combo deal" },
      { status: 500 }
    );
  }
}
