"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Bell, Check, AlertCircle } from "lucide-react";
import { toast } from "sonner";

interface StockNotificationProps {
  productId: string;
  productName: string;
  selectedSize?: string;
  isOutOfStock: boolean;
}

export default function StockNotification({ 
  productId, 
  productName, 
  selectedSize, 
  isOutOfStock 
}: StockNotificationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/stock-notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          productId,
          size: selectedSize,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setIsSubscribed(true);
        toast.success("You'll be notified when this item is back in stock!");
        setTimeout(() => {
          setIsOpen(false);
          setEmail("");
          setIsSubscribed(false);
        }, 2000);
      } else {
        toast.error(result.error || "Failed to set up notification");
      }
    } catch (error) {
      console.error("Error setting up stock notification:", error);
      toast.error("Failed to set up notification. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOutOfStock) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full flex items-center gap-2">
          <Bell className="h-4 w-4" />
          Notify When Available
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Stock Notification
          </DialogTitle>
        </DialogHeader>

        {!isSubscribed ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-orange-600 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-orange-900">Out of Stock</p>
                  <p className="text-xs text-orange-700">
                    {productName}
                    {selectedSize && (
                      <Badge variant="outline" className="ml-2">
                        Size {selectedSize}
                      </Badge>
                    )}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <p className="text-xs text-gray-600">
                We'll send you an email as soon as this item is back in stock.
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? "Setting up..." : "Notify Me"}
              </Button>
            </div>
          </form>
        ) : (
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium text-green-900">All Set!</h3>
              <p className="text-sm text-green-700 mt-1">
                You'll receive an email notification when this item is back in stock.
              </p>
            </div>
          </div>
        )}

        <div className="text-xs text-gray-500 text-center">
          <p>
            By subscribing, you agree to receive stock notifications. 
            You can unsubscribe at any time.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
