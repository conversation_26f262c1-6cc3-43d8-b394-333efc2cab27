"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import NavBar from "@/components/navbar";
import ProductImageGallery from "@/components/products/product-image-gallery";
import ProductInfo from "@/components/products/product-info";
import ProductTabs from "@/components/products/product-tabs";
import RelatedProducts from "@/components/products/related-products";
import SmartRecommendations from "@/components/products/smart-recommendations";
import ProductMetaTags from "@/components/seo/product-meta-tags";
import CustomRequestForm from "@/components/products/custom-request-form";
import { useSession } from "@/lib/auth-client";
import { Product, User } from "@/utils/types";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { getUserById } from "@/actions/userActions";
import { getEffectivePrice, isProductOnSale } from "@/lib/product-utils";
import NavBarSkeleton from "@/components/navBarSkeleton";
import { useRecentlyViewed } from "@/contexts/recently-viewed-context";
import RecentlyViewed from "@/components/products/recently-viewed";

export default function ProductDetailPage() {
  const { data: session, isPending } = useSession();
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { addToRecentlyViewed } = useRecentlyViewed();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch user data if session exists
        if (session?.user?.id) {
          try {
            const userResponse = await getUserById(session.user.id);
            if (userResponse.success && userResponse.data) {
              setUser(userResponse.data);
            }
          } catch (error) {
            console.error("Error fetching user:", error);
          }
        }

        // Fetch product details
        const response = await fetch(`/api/products/${productId}`);
        const result = await response.json();

        // Fetch all products for recommendations
        const allProductsResponse = await fetch('/api/products?limit=100');
        const allProductsResult = await allProductsResponse.json();

        if (result.success) {
          setProduct(result.data);
          // Add to recently viewed
          addToRecentlyViewed(result.data);

          if (allProductsResult.success) {
            setAllProducts(allProductsResult.data.data || []);
          }
        } else {
          setError(result.error || "Product not found");
        }
      } catch (err) {
        setError("Failed to fetch product");
        console.error("Error fetching product:", err);
      } finally {
        setLoading(false);
      }
    };

    if (!isPending && productId) {
      fetchData();
    }
  }, [productId, session, isPending]);

  // if (!session) {
  //   router.push("/sign-in")
  //   return (
  //     <div className="w-full h-screen flex items-center justify-center">
  //       <SpinnerCircle4 />
  //     </div>
  //   );
  // }

  if (loading || isPending) {
    return (
      <div className="min-h-screen bg-gray-50">
        {user && <NavBar user={user} />}
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center py-12">
            <SpinnerCircle4 />
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50">
        {user && <NavBar user={user} />}
        <div className="container mx-auto px-4 py-6">
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Product not found
            </h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <Link href="/products">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const effectivePrice = getEffectivePrice(product);
  const isOnSale = isProductOnSale(product);

  return (
    <div className="min-h-screen bg-gray-50">
      <ProductMetaTags
        product={product}
        effectivePrice={effectivePrice}
        isOnSale={isOnSale}
      />

      {user ? (
        <NavBar user={user} />
      ) : (
        <NavBarSkeleton loading={isPending} user={null} />
      )}

      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
          <Link href="/products" className="hover:text-gray-700">
            Products
          </Link>
          <span>/</span>
          <Link
            href={`/products?categoryId=${product.categoryId}`}
            className="hover:text-gray-700"
          >
            {product.category.name}
          </Link>
          <span>/</span>
          <span className="text-gray-900">{product.name}</span>
        </nav>

        {/* Product Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product Images */}
          <div>
            <ProductImageGallery
              images={product.images}
              productName={product.name}
            />
          </div>

          {/* Product Info */}
          <div>
            <ProductInfo product={product} />
          </div>
        </div>

        {/* Product Tabs (Description, Reviews, etc.) */}
        <div className="mb-12">
          <ProductTabs product={product} />
        </div>

        {/* Custom Request Form */}
        <div className="mb-12">
          <CustomRequestForm />
        </div>

        {/* Smart Recommendations */}
        <div className="mb-12">
          <SmartRecommendations
            currentProduct={product}
            allProducts={allProducts}
            maxRecommendations={8}
          />
        </div>

        {/* Related Products */}
        <div className="mb-12">
          <RelatedProducts
            currentProduct={product}
            categoryId={product.categoryId}
          />
        </div>

        {/* Recently Viewed Products */}
        <div>
          <RecentlyViewed />
        </div>
      </div>
    </div>
  );
}
