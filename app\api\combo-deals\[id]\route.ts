import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/combo-deals/[id] - Get single combo deal for public display
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    const combo = await prisma.comboDeal.findUnique({
      where: { 
        id,
        isActive: true,
        validUntil: {
          gt: new Date(),
        },
      },
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                discountedPrice: true,
                images: true,
                stock: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    if (!combo) {
      return NextResponse.json(
        { success: false, error: "Combo deal not found or expired" },
        { status: 404 }
      );
    }

    // Check if all products are available
    const allProductsAvailable = combo.comboProducts.every(cp => 
      cp.product.isActive && cp.product.stock > 0
    );

    if (!allProductsAvailable) {
      return NextResponse.json(
        { success: false, error: "Some products in this combo are no longer available" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: combo,
    });
  } catch (error) {
    console.error("Error fetching combo deal:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch combo deal" },
      { status: 500 }
    );
  }
}
