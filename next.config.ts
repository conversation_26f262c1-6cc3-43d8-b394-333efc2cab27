import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    formats: ["image/avif", "image/webp"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "2iwp56ev9j.ufs.sh",
        pathname: "/f/**",
      }, {
        protocol: "https",
        hostname: "utfs.io",
        pathname: "/f/**",
      }
    ],
  },
};

export default nextConfig;
