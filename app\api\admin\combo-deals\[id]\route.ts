import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-utils";

// GET /api/admin/combo-deals/[id] - Get single combo deal
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin();
    const { id } = await context.params;

    const combo = await prisma.comboDeal.findUnique({
      where: { id },
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                discountedPrice: true,
                images: true,
                stock: true,
              },
            },
          },
        },
        _count: {
          select: {
            cartItems: true,
            orderItems: true,
          },
        },
      },
    });

    if (!combo) {
      return NextResponse.json(
        { success: false, error: "Combo deal not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: combo,
    });
  } catch (error) {
    console.error("Error fetching combo deal:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch combo deal" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/combo-deals/[id] - Update combo deal
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAdmin();
    const { id } = await context.params;
    const body = await request.json();

    const {
      name,
      description,
      comboPrice,
      validUntil,
      isActive,
      isFeatured,
      productIds,
    } = body;

    // Check if combo exists
    const existingCombo = await prisma.comboDeal.findUnique({
      where: { id },
      include: { comboProducts: true },
    });

    if (!existingCombo) {
      return NextResponse.json(
        { success: false, error: "Combo deal not found" },
        { status: 404 }
      );
    }

    // If productIds are provided, validate and recalculate prices
    let updateData: any = {
      name,
      description,
      validUntil: validUntil ? new Date(validUntil) : undefined,
      isActive,
      isFeatured,
    };

    if (productIds && productIds.length > 0) {
      // Validate that all products exist
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true, price: true, discountedPrice: true },
      });

      if (products.length !== productIds.length) {
        return NextResponse.json(
          { success: false, error: "Some products not found" },
          { status: 400 }
        );
      }

      // Calculate new original price
      const originalPrice = products.reduce((sum, product) => {
        return sum + (product.discountedPrice || product.price);
      }, 0);

      const finalComboPrice = comboPrice || existingCombo.comboPrice;
      const discount = originalPrice - finalComboPrice;
      const discountPercent = (discount / originalPrice) * 100;

      if (discount <= 0) {
        return NextResponse.json(
          { success: false, error: "Combo price must be less than original price" },
          { status: 400 }
        );
      }

      updateData = {
        ...updateData,
        originalPrice,
        comboPrice: finalComboPrice,
        discount,
        discountPercent,
      };
    } else if (comboPrice && comboPrice !== existingCombo.comboPrice) {
      // Only update combo price
      const discount = existingCombo.originalPrice - comboPrice;
      const discountPercent = (discount / existingCombo.originalPrice) * 100;

      if (discount <= 0) {
        return NextResponse.json(
          { success: false, error: "Combo price must be less than original price" },
          { status: 400 }
        );
      }

      updateData = {
        ...updateData,
        comboPrice,
        discount,
        discountPercent,
      };
    }

    // Update combo deal
    const combo = await prisma.comboDeal.update({
      where: { id },
      data: updateData,
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                images: true,
              },
            },
          },
        },
      },
    });

    // Update products if provided
    if (productIds && productIds.length > 0) {
      // Delete existing combo products
      await prisma.comboProduct.deleteMany({
        where: { comboId: id },
      });

      // Create new combo products
      await prisma.comboProduct.createMany({
        data: productIds.map((productId: string) => ({
          comboId: id,
          productId,
          quantity: 1,
        })),
      });

      // Fetch updated combo with new products
      const updatedCombo = await prisma.comboDeal.findUnique({
        where: { id },
        include: {
          comboProducts: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  brand: true,
                  price: true,
                  images: true,
                },
              },
            },
          },
        },
      });

      return NextResponse.json({
        success: true,
        data: updatedCombo,
        message: "Combo deal updated successfully",
      });
    }

    return NextResponse.json({
      success: true,
      data: combo,
      message: "Combo deal updated successfully",
    });
  } catch (error) {
    console.error("Error updating combo deal:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update combo deal" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/combo-deals/[id] - Delete combo deal
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin();
    const { id } = await context.params;

    // Check if combo exists
    const existingCombo = await prisma.comboDeal.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            cartItems: true,
            orderItems: true,
          },
        },
      },
    });

    if (!existingCombo) {
      return NextResponse.json(
        { success: false, error: "Combo deal not found" },
        { status: 404 }
      );
    }

    // Check if combo has been used in orders
    if (existingCombo._count.orderItems > 0) {
      return NextResponse.json(
        { success: false, error: "Cannot delete combo deal that has been ordered" },
        { status: 400 }
      );
    }

    // Delete combo deal (cascade will handle combo products and cart items)
    await prisma.comboDeal.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "Combo deal deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting combo deal:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete combo deal" },
      { status: 500 }
    );
  }
}
