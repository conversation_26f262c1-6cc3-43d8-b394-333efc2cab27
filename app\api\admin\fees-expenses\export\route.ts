import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { getFeeSettings } from "@/lib/fee-utils";

// GET /api/admin/fees-expenses/export - Export fees and expenses report
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get fee settings for calculations
    const feeSettings = await getFeeSettings();

    // Get all orders with their items and fee breakdowns
    const orders = await prisma.order.findMany({
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                brand: true,
              },
            },
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Prepare CSV data
    const csvRows = [];
    
    // CSV Header
    csvRows.push([
      'Order ID',
      'Order Number',
      'Customer Name',
      'Order Date',
      'Status',
      'Delivered Date',
      'Shoe Name',
      'Shoe Brand',
      'Quantity',
      'Sale Price',
      'Cost Price',
      'Shipping Fee',
      'Delivery Fee',
      'Late Collection Fee',
      'Bulk Delivery Discount',
      'Total Expenses',
      'Net Profit',
      'Is Bulk Delivery',
      'Delivery Fee Paid',
      'Delivery Fee Paid Date'
    ]);

    // Process each order and its items
    orders.forEach(order => {
      order.orderItems.filter(item => item.product !== null).forEach(item => {
        const orderShippingFees = (item.shippingFee || 0) * item.quantity;
        const orderLateFees = (item.lateCollectionFee || 0) * item.quantity;
        const orderDeliveryFee = order.deliveryFee ? (order.deliveryFee / order.orderItems.reduce((sum, oi) => sum + oi.quantity, 0)) * item.quantity : 0;
        const bulkDiscount = order.isBulkDelivery ? ((feeSettings.defaultDeliveryFee - feeSettings.defaultBulkDeliveryFee) * item.quantity) : 0;
        const totalExpenses = (item.costPrice || 0) + orderShippingFees + orderDeliveryFee + orderLateFees;
        const netProfit = (item.price * item.quantity) - totalExpenses;

        csvRows.push([
          order.id,
          order.orderNumber,
          order.user?.name || 'Unknown Customer',
          order.createdAt.toISOString().split('T')[0],
          order.status,
          order.deliveredAt ? order.deliveredAt.toISOString().split('T')[0] : '',
          item.product!.name,
          item.product!.brand,
          item.quantity.toString(),
          (item.price * item.quantity).toFixed(2),
          (item.costPrice || 0).toFixed(2),
          orderShippingFees.toFixed(2),
          orderDeliveryFee.toFixed(2),
          orderLateFees.toFixed(2),
          bulkDiscount.toFixed(2),
          totalExpenses.toFixed(2),
          netProfit.toFixed(2),
          order.isBulkDelivery ? 'Yes' : 'No',
          order.deliveryFeePaid ? 'Yes' : 'No',
          order.deliveryFeePaidAt ? order.deliveryFeePaidAt.toISOString().split('T')[0] : ''
        ]);
      });
    });

    // Convert to CSV string
    const csvContent = csvRows.map(row => 
      row.map(field => `"${field}"`).join(',')
    ).join('\n');

    // Create response with CSV headers
    const response = new NextResponse(csvContent);
    response.headers.set('Content-Type', 'text/csv');
    response.headers.set('Content-Disposition', `attachment; filename="fees-expenses-report-${new Date().toISOString().split('T')[0]}.csv"`);

    return response;
  } catch (error) {
    console.error("Error exporting fees and expenses:", error);
    return NextResponse.json(
      { success: false, error: "Failed to export fees and expenses" },
      { status: 500 }
    );
  }
} 