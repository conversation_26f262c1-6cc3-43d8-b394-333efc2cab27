import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// PATCH /api/admin/lay-buy-cancellation-requests/[id] - Process cancellation request
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: requestId } = await context.params;
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, adminNotes, refundAmount } = body;

    if (!["APPROVE", "REJECT"].includes(action)) {
      return NextResponse.json(
        { success: false, error: "Invalid action. Must be APPROVE or REJECT" },
        { status: 400 }
      );
    }

    // Get the cancellation request
    const cancellationRequest = await prisma.layBuyCancellationRequest.findUnique({
      where: { id: requestId },
      include: {
        layBuyOrder: {
          include: {
            user: true,
            payments: {
              where: { status: "VERIFIED" },
            },
            orderItems: {
              include: {
                product: true,
              },
            },
          },
        },
      },
    });

    if (!cancellationRequest) {
      return NextResponse.json(
        { success: false, error: "Cancellation request not found" },
        { status: 404 }
      );
    }

    if (cancellationRequest.status !== "PENDING") {
      return NextResponse.json(
        { success: false, error: "Request has already been processed" },
        { status: 400 }
      );
    }

    if (cancellationRequest.layBuyOrder.status !== "ACTIVE") {
      return NextResponse.json(
        { success: false, error: "Can only process requests for active orders" },
        { status: 400 }
      );
    }

    // Process the request in a transaction
    const result = await prisma.$transaction(async (tx) => {
      if (action === "APPROVE") {
        // Update the cancellation request
        const updatedRequest = await tx.layBuyCancellationRequest.update({
          where: { id: requestId },
          data: {
            status: "APPROVED",
            adminNotes,
            processedBy: user.id,
            processedAt: new Date(),
            refundAmount: refundAmount || cancellationRequest.refundAmount,
          },
        });

        // Update the Lay-Buy order status
        const updatedOrder = await tx.layBuyOrder.update({
          where: { id: cancellationRequest.layBuyOrderId },
          data: {
            status: "CANCELLED",
            cancelledAt: new Date(),
            refundAmount: refundAmount || cancellationRequest.refundAmount,
            adminNotes: adminNotes ? 
              `${cancellationRequest.layBuyOrder.adminNotes || ""}\n\nCancellation approved: ${adminNotes}`.trim() :
              `${cancellationRequest.layBuyOrder.adminNotes || ""}\n\nCancellation approved by admin`.trim(),
          },
        });

        // Restore product stock
        for (const item of cancellationRequest.layBuyOrder.orderItems) {
          if (item.productId !== null) {
            await tx.product.update({
              where: { id: item.productId },
              data: {
                stock: {
                  increment: item.quantity,
                },
              },
            });
          }
        }

        return { request: updatedRequest, order: updatedOrder };
      } else {
        // Reject the request
        const updatedRequest = await tx.layBuyCancellationRequest.update({
          where: { id: requestId },
          data: {
            status: "REJECTED",
            adminNotes,
            processedBy: user.id,
            processedAt: new Date(),
          },
        });

        return { request: updatedRequest, order: cancellationRequest.layBuyOrder };
      }
    });

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error processing cancellation request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process cancellation request" },
      { status: 500 }
    );
  }
}

// GET /api/admin/lay-buy-cancellation-requests/[id] - Get specific cancellation request
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: requestId } = await context.params;
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const cancellationRequest = await prisma.layBuyCancellationRequest.findUnique({
      where: { id: requestId },
      include: {
        layBuyOrder: {
          include: {
            orderItems: {
              include: {
                product: true,
              },
            },
            payments: {
              orderBy: { createdAt: "desc" },
            },
          },
        },
        requestedBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!cancellationRequest) {
      return NextResponse.json(
        { success: false, error: "Cancellation request not found" },
        { status: 404 }
      );
    }

    const response: ApiResponse<typeof cancellationRequest> = {
      success: true,
      data: cancellationRequest,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching cancellation request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch cancellation request" },
      { status: 500 }
    );
  }
}
