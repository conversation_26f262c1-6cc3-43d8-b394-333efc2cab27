import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { sendEmail, EMAIL_CONFIG } from "@/lib/email";

// Validation schema for custom request
const customRequestSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  email: z.string().email("Invalid email address"),
  shoeName: z.string().min(1, "Shoe name is required").max(200, "Shoe name too long"),
  brand: z.string().max(100, "Brand name too long").optional(),
  color: z.string().max(100, "Color description too long").optional(),
  size: z.string().max(10, "Size too long").optional(),
  shoeType: z.string().max(50, "Shoe type too long").optional(),
  description: z.string().min(20, "Description must be at least 20 characters").max(500, "Description too long"),
  imageUrl: z.string().url().optional().or(z.literal("")),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validationResult = customRequestSchema.safeParse(body);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message);
      return NextResponse.json(
        { success: false, error: errors[0] || "Invalid input data" },
        { status: 400 }
      );
    }

    const data = validationResult.data;
    
    // Get current user if authenticated
    const user = await getCurrentUser();

    // Create contact message with custom request details
    const subject = `Custom Shoe Request: ${data.shoeName}`;
    const message = `
CUSTOM SHOE REQUEST

Shoe Details:
- Name: ${data.shoeName}
- Brand: ${data.brand || 'Not specified'}
- Color: ${data.color || 'Not specified'}
- Size: ${data.size || 'Not specified'}
- Type: ${data.shoeType || 'Not specified'}

Description:
${data.description}

${data.imageUrl ? `Image: ${data.imageUrl}` : 'No image provided'}

Customer Contact:
- Name: ${data.name}
- Email: ${data.email}
    `.trim();

    const contactMessage = await prisma.contactMessage.create({
      data: {
        name: data.name.trim(),
        email: data.email.trim().toLowerCase(),
        subject: subject,
        message: message,
        userId: user?.id || null,
        status: "UNREAD",
      },
    });

    // Send confirmation email to customer
    const customerEmailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Custom Request Confirmation</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="margin: 0; font-size: 28px;">RIVV Premium Sneakers</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Custom Request Confirmation</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #667eea; margin-top: 0;">Thank you for your custom request!</h2>
            
            <p>Hi ${data.name},</p>
            
            <p>We've received your custom shoe request and our team will review it shortly. Here's a summary of what you requested:</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
              <h3 style="margin-top: 0; color: #333;">Requested Shoe Details</h3>
              <ul style="list-style: none; padding: 0;">
                <li style="margin: 8px 0;"><strong>Shoe Name:</strong> ${data.shoeName}</li>
                ${data.brand ? `<li style="margin: 8px 0;"><strong>Brand:</strong> ${data.brand}</li>` : ''}
                ${data.color ? `<li style="margin: 8px 0;"><strong>Color:</strong> ${data.color}</li>` : ''}
                ${data.size ? `<li style="margin: 8px 0;"><strong>Size:</strong> ${data.size}</li>` : ''}
                ${data.shoeType ? `<li style="margin: 8px 0;"><strong>Type:</strong> ${data.shoeType}</li>` : ''}
              </ul>
              <p><strong>Description:</strong></p>
              <p style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">${data.description}</p>
            </div>
            
            <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1976d2;">What happens next?</h3>
              <ul style="color: #1976d2; margin: 0;">
                <li>Our team will review your request within 24 hours</li>
                <li>We'll search our supplier network for the shoe you requested</li>
                <li>You'll receive an email with availability and pricing information</li>
                <li>If available, we'll provide ordering and delivery details</li>
              </ul>
            </div>
            
            <p>If you have any questions or need to modify your request, please reply to this email or contact us at:</p>
            <ul>
              <li>Email: <EMAIL></li>
              <li>Phone: +266 62844473</li>
            </ul>
            
            <p>Thank you for choosing RIVV Premium Sneakers!</p>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
              <p style="color: #666; font-size: 14px;">
                RIVV Premium Sneakers<br>
                Premium footwear delivered to your door<br>
                <a href="${EMAIL_CONFIG.baseUrl}" style="color: #667eea;">Visit our website</a>
              </p>
            </div>
          </div>
        </body>
      </html>
    `;

    const customerEmailText = `
      RIVV Premium Sneakers - Custom Request Confirmation
      
      Hi ${data.name},
      
      We've received your custom shoe request for: ${data.shoeName}
      
      Our team will review your request within 24 hours and get back to you with availability and pricing information.
      
      If you have any questions, contact <NAME_EMAIL> or +266 62844473.
      
      Thank you for choosing RIVV Premium Sneakers!
    `;

    // Send confirmation email to customer
    await sendEmail({
      to: data.email,
      subject: "Custom Request Received - RIVV Premium Sneakers",
      html: customerEmailHtml,
      text: customerEmailText,
    });

    // Send notification email to admin
    const adminEmailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>New Custom Request</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #dc3545; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="margin: 0; font-size: 24px;">🔔 New Custom Request</h1>
            <p style="margin: 10px 0 0 0;">RIVV Premium Sneakers</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #dc3545; margin-top: 0;">Customer Details</h2>
            <ul>
              <li><strong>Name:</strong> ${data.name}</li>
              <li><strong>Email:</strong> ${data.email}</li>
            </ul>
            
            <h2 style="color: #dc3545;">Requested Shoe</h2>
            <ul>
              <li><strong>Name:</strong> ${data.shoeName}</li>
              ${data.brand ? `<li><strong>Brand:</strong> ${data.brand}</li>` : ''}
              ${data.color ? `<li><strong>Color:</strong> ${data.color}</li>` : ''}
              ${data.size ? `<li><strong>Size:</strong> ${data.size}</li>` : ''}
              ${data.shoeType ? `<li><strong>Type:</strong> ${data.shoeType}</li>` : ''}
            </ul>
            
            <h2 style="color: #dc3545;">Description</h2>
            <p style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;">${data.description}</p>
            
            ${data.imageUrl ? `
              <h2 style="color: #dc3545;">Uploaded Image</h2>
              <p><a href="${data.imageUrl}" target="_blank" style="color: #dc3545;">View uploaded image</a></p>
            ` : ''}
            
            <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px; border-left: 4px solid #ffc107;">
              <p style="margin: 0;"><strong>Action Required:</strong> Review this request and respond to the customer within 24 hours.</p>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
              <a href="${EMAIL_CONFIG.baseUrl}/admin/contact" style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">View in Admin Panel</a>
            </div>
          </div>
        </body>
      </html>
    `;

    // Send notification to admin
    await sendEmail({
      to: "<EMAIL>",
      subject: `🔔 New Custom Request: ${data.shoeName}`,
      html: adminEmailHtml,
      text: `New custom request from ${data.name} (${data.email}) for: ${data.shoeName}\n\n${message}`,
    });

    return NextResponse.json({
      success: true,
      message: "Custom request submitted successfully",
      data: { id: contactMessage.id }
    });

  } catch (error) {
    console.error("Error processing custom request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to submit custom request" },
      { status: 500 }
    );
  }
}
