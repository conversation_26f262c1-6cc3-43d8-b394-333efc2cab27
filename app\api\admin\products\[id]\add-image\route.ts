import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Helper function to require admin access
async function requireAdmin() {
  const user = await getCurrentUser();
  if (!user || user.role !== "ADMIN") {
    throw new Error("Admin access required");
  }
  return user;
}

// POST /api/admin/products/[id]/add-image - Add a new image to product
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await context.params;
    
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const { imageUrl } = body;

    // Validate required fields
    if (!imageUrl || typeof imageUrl !== "string") {
      return NextResponse.json(
        { success: false, error: "Image URL is required" },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(imageUrl);
    } catch {
      return NextResponse.json(
        { success: false, error: "Invalid image URL format" },
        { status: 400 }
      );
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, images: true, name: true },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if image already exists
    if (existingProduct.images.includes(imageUrl)) {
      return NextResponse.json(
        { success: false, error: "Image already exists for this product" },
        { status: 400 }
      );
    }

    // Add the new image to the images array
    const newImages = [...existingProduct.images, imageUrl];

    // Update the product with the new images array
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        images: newImages,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        images: true,
        updatedAt: true,
      },
    });

    const response: ApiResponse<typeof updatedProduct> = {
      success: true,
      data: updatedProduct,
      message: `Image added successfully. Product now has ${newImages.length} images.`,
    };

    console.log(`[ADMIN] Image added to product ${existingProduct.name} (${productId}): ${imageUrl}`);

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error adding product image:", error);
    
    if (error instanceof Error && error.message === "Admin access required") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to add image" },
      { status: 500 }
    );
  }
}
