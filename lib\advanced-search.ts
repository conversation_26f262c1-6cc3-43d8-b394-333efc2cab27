/**
 * Advanced Search System with Fuzzy Matching and Relevance Scoring
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface SearchOptions {
  query: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  colors?: string[];
  sizes?: string[];
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'rating' | 'newest';
  limit?: number;
  offset?: number;
  includeInactive?: boolean;
}

export interface SearchResult {
  products: any[];
  total: number;
  suggestions: string[];
  filters: {
    brands: { name: string; count: number }[];
    categories: { name: string; count: number }[];
    priceRange: { min: number; max: number };
    colors: { name: string; count: number }[];
  };
}

/**
 * Calculate similarity between two strings using Levenshtein distance
 */
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Calculate search relevance score for a product
 */
function calculateRelevanceScore(product: any, searchTerms: string[]): number {
  let score = 0;
  const queryLower = searchTerms.join(' ').toLowerCase();
  
  // Exact name match gets highest score
  if (product.name.toLowerCase().includes(queryLower)) {
    score += 100;
  }
  
  // Brand match
  if (searchTerms.some(term => product.brand.toLowerCase().includes(term.toLowerCase()))) {
    score += 80;
  }
  
  // Tag matches
  const tagMatches = product.tags?.filter((tag: string) => 
    searchTerms.some(term => tag.toLowerCase().includes(term.toLowerCase()))
  ).length || 0;
  score += tagMatches * 15;
  
  // Keyword matches
  const keywordMatches = product.keywords?.filter((keyword: string) => 
    searchTerms.some(term => calculateSimilarity(keyword.toLowerCase(), term.toLowerCase()) > 0.7)
  ).length || 0;
  score += keywordMatches * 10;
  
  // Search terms matches
  const searchTermMatches = product.searchTerms?.filter((searchTerm: string) => 
    searchTerms.some(term => searchTerm.toLowerCase().includes(term.toLowerCase()))
  ).length || 0;
  score += searchTermMatches * 8;
  
  // Description match
  if (product.description && searchTerms.some(term => 
    product.description.toLowerCase().includes(term.toLowerCase())
  )) {
    score += 20;
  }
  
  // Color matches
  const colorMatches = product.colors?.filter((color: string) => 
    searchTerms.some(term => color.toLowerCase().includes(term.toLowerCase()))
  ).length || 0;
  score += colorMatches * 12;
  
  // Fuzzy matching for partial matches
  searchTerms.forEach(term => {
    const termLower = term.toLowerCase();
    
    // Check name similarity
    const nameSimilarity = calculateSimilarity(product.name.toLowerCase(), termLower);
    if (nameSimilarity > 0.6) {
      score += nameSimilarity * 30;
    }
    
    // Check brand similarity
    const brandSimilarity = calculateSimilarity(product.brand.toLowerCase(), termLower);
    if (brandSimilarity > 0.7) {
      score += brandSimilarity * 25;
    }
  });
  
  // Boost score based on product quality metrics
  score += (product.searchScore || 0) * 0.3;
  score += (product.rating || 0) * 5;
  score += Math.min((product.reviewCount || 0) * 0.1, 10);
  
  // Boost for in-stock items
  if (product.stock > 0) {
    score += 15;
  }
  
  // Boost for active products
  if (product.isActive) {
    score += 10;
  }
  
  return score;
}

/**
 * Generate search suggestions based on query
 */
async function generateSearchSuggestions(query: string): Promise<string[]> {
  const queryLower = query.toLowerCase();
  
  // Get popular search terms from products
  const products = await prisma.product.findMany({
    select: {
      name: true,
      brand: true,
      tags: true,
      keywords: true,
      searchTerms: true
    },
    where: { isActive: true },
    take: 100
  });
  
  const suggestions = new Set<string>();
  
  products.forEach(product => {
    // Add brand suggestions
    if (product.brand.toLowerCase().includes(queryLower)) {
      suggestions.add(product.brand);
    }
    
    // Add tag suggestions
    product.tags?.forEach(tag => {
      if (tag.toLowerCase().includes(queryLower)) {
        suggestions.add(tag);
      }
    });
    
    // Add keyword suggestions
    product.keywords?.forEach(keyword => {
      if (keyword.toLowerCase().includes(queryLower) && keyword.length > 2) {
        suggestions.add(keyword);
      }
    });
    
    // Add product name suggestions
    if (product.name.toLowerCase().includes(queryLower)) {
      suggestions.add(product.name);
    }
  });
  
  return Array.from(suggestions).slice(0, 8);
}

/**
 * Main search function with advanced filtering and scoring
 */
export async function advancedSearch(options: SearchOptions): Promise<SearchResult> {
  const {
    query,
    category,
    brand,
    minPrice,
    maxPrice,
    colors,
    sizes,
    sortBy = 'relevance',
    limit = 20,
    offset = 0,
    includeInactive = false
  } = options;
  
  const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
  
  // Build where clause
  const whereClause: any = {
    isActive: includeInactive ? undefined : true,
    ...(category && { category: { name: { contains: category, mode: 'insensitive' } } }),
    ...(brand && { brand: { contains: brand, mode: 'insensitive' } }),
    ...(minPrice && { price: { gte: minPrice } }),
    ...(maxPrice && { price: { lte: maxPrice } }),
    ...(colors && colors.length > 0 && {
      colors: { hasSome: colors }
    }),
    ...(sizes && sizes.length > 0 && {
      sizes: { hasSome: sizes }
    })
  };
  
  // Add search conditions
  if (searchTerms.length > 0) {
    whereClause.OR = [
      { name: { contains: query, mode: 'insensitive' } },
      { brand: { contains: query, mode: 'insensitive' } },
      { description: { contains: query, mode: 'insensitive' } },
      { tags: { hasSome: searchTerms } },
      { keywords: { hasSome: searchTerms } },
      { searchTerms: { hasSome: searchTerms } },
      ...searchTerms.map(term => ({
        OR: [
          { name: { contains: term, mode: 'insensitive' } },
          { brand: { contains: term, mode: 'insensitive' } },
          { tags: { has: term } },
          { keywords: { has: term } },
          { searchTerms: { has: term } }
        ]
      }))
    ];
  }
  
  // Get products
  const products = await prisma.product.findMany({
    where: whereClause,
    include: {
      category: true
    },
    take: limit * 3, // Get more for better sorting
    skip: offset
  });
  
  // Calculate relevance scores and sort
  const scoredProducts = products.map(product => ({
    ...product,
    relevanceScore: searchTerms.length > 0 ? calculateRelevanceScore(product, searchTerms) : product.searchScore || 0
  }));
  
  // Sort products
  let sortedProducts = scoredProducts;
  switch (sortBy) {
    case 'relevance':
      sortedProducts = scoredProducts.sort((a, b) => b.relevanceScore - a.relevanceScore);
      break;
    case 'price_asc':
      sortedProducts = scoredProducts.sort((a, b) => a.price - b.price);
      break;
    case 'price_desc':
      sortedProducts = scoredProducts.sort((a, b) => b.price - a.price);
      break;
    case 'rating':
      sortedProducts = scoredProducts.sort((a, b) => b.rating - a.rating);
      break;
    case 'newest':
      sortedProducts = scoredProducts.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      break;
  }
  
  // Take only the requested amount
  const finalProducts = sortedProducts.slice(0, limit);
  
  // Get total count
  const total = await prisma.product.count({ where: whereClause });
  
  // Generate suggestions
  const suggestions = query ? await generateSearchSuggestions(query) : [];
  
  // Get filter data
  const allProducts = await prisma.product.findMany({
    where: { isActive: includeInactive ? undefined : true },
    select: {
      brand: true,
      category: {
        select: {
          name: true
        }
      },
      price: true,
      colors: true
    }
  });
  
  const filters = {
    brands: Object.entries(
      allProducts.reduce((acc, p) => {
        acc[p.brand] = (acc[p.brand] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    ).map(([name, count]) => ({ name, count })).slice(0, 20),
    
    categories: Object.entries(
      allProducts.reduce((acc, p) => {
        acc[p.category.name] = (acc[p.category.name] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    ).map(([name, count]) => ({ name, count })),
    
    priceRange: {
      min: Math.min(...allProducts.map(p => p.price)),
      max: Math.max(...allProducts.map(p => p.price))
    },
    
    colors: Object.entries(
      allProducts.reduce((acc, p) => {
        p.colors.forEach(color => {
          acc[color] = (acc[color] || 0) + 1;
        });
        return acc;
      }, {} as Record<string, number>)
    ).map(([name, count]) => ({ name, count })).slice(0, 15)
  };
  
  return {
    products: finalProducts,
    total,
    suggestions,
    filters
  };
}
