"use client";

import { useCart } from "@/contexts/cart-context";
import { useCartWithReferral } from "@/hooks/use-cart-with-referral";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { formatPrice, calculateDeliveryFee } from "@/lib/product-utils";
import { ShoppingCart } from "lucide-react";
import Link from "next/link";

interface CartSummaryProps {
  showCheckoutButton?: boolean;
  className?: string;
}

export default function CartSummary({ showCheckoutButton = true, className = "" }: CartSummaryProps) {
  const { state: cartState } = useCartWithReferral();

  if (cartState.items.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-500 mb-4">Your cart is empty</p>
          <Link href="/products">
            <Button variant="outline" size="sm">
              Start Shopping
            </Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  const subtotal = cartState.totalPrice;
  const deliveryInfo = calculateDeliveryFee("Maseru", subtotal);
  const deliveryCost = deliveryInfo.fee;
  const total = subtotal + deliveryCost;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          Cart Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Items Summary */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Items ({cartState.totalItems})</span>
            <span>{formatPrice(subtotal)}</span>
          </div>

          {cartState.totalReferralSavings > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Referral Discount</span>
              <span>-{formatPrice(cartState.totalReferralSavings)}</span>
            </div>
          )}

          <div className="flex justify-between text-sm">
            <span>Delivery</span>
            <span className={deliveryInfo.isFree ? "text-green-600" : ""}>
              {deliveryInfo.isFree ? "Free" : formatPrice(deliveryCost)}
            </span>
          </div>

          {deliveryInfo.reason && (
            <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded mt-1">{deliveryInfo.reason}</div>
          )}
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between font-semibold text-lg">
          <span>Total</span>
          <span>{formatPrice(total)}</span>
        </div>

        {/* Action Buttons */}
        {showCheckoutButton && (
          <div className="space-y-2">
            <Link href="/checkout" className="w-full">
              <Button size="lg" className="w-full">
                Proceed to Checkout
              </Button>
            </Link>
            
            <Link href="/cart" className="w-full">
              <Button variant="outline" size="sm" className="w-full">
                View Cart
              </Button>
            </Link>
          </div>
        )}

        {/* Security Features */}
        <div className="pt-4 border-t">
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Secure checkout</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
