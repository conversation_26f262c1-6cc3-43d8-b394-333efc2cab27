"use client";

import { CartItem } from "@/contexts/cart-context";
import { useReferral } from "@/contexts/referral-context";
import { getEffectivePriceWithReferral } from "@/lib/product-utils";

interface CartItemWithReferralProps {
  item: CartItem;
  children: (effectivePrice: number, referralSavings: number) => React.ReactNode;
}

export default function CartItemWithReferral({ item, children }: CartItemWithReferralProps) {
  const { referralDiscount } = useReferral();
  
  const basePrice = item.product.discountedPrice || item.product.price;
  const effectivePrice = getEffectivePriceWithReferral(item.product, referralDiscount);
  const referralSavings = basePrice - effectivePrice;
  
  return <>{children(effectivePrice, referralSavings)}</>;
}
