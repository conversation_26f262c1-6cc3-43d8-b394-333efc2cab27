/**
 * API Key Rotation Manager for Google AI API
 * Handles automatic rotation, rate limiting, and fallback
 */

interface APIKeyStatus {
  key: string;
  isActive: boolean;
  lastUsed: number;
  requestCount: number;
  errorCount: number;
  quotaResetTime: number;
  dailyRequestCount: number;
  lastDailyReset: number;
}

interface APIKeyConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerDay: number;
  maxErrorsBeforeDisable: number;
  cooldownPeriod: number; // milliseconds
}

class APIKeyManager {
  private keys: APIKeyStatus[] = [];
  private currentKeyIndex: number = 0;
  private config: APIKeyConfig;

  constructor(apiKeys: string[], config?: Partial<APIKeyConfig>) {
    this.config = {
      maxRequestsPerMinute: 60,
      maxRequestsPerDay: 1500,
      maxErrorsBeforeDisable: 5,
      cooldownPeriod: 60000, // 1 minute
      ...config
    };

    // Initialize API keys
    this.keys = apiKeys.map(key => ({
      key,
      isActive: true,
      lastUsed: 0,
      requestCount: 0,
      errorCount: 0,
      quotaResetTime: Date.now() + 60000, // Reset every minute
      dailyRequestCount: 0,
      lastDailyReset: Date.now()
    }));
  }

  /**
   * Get the next available API key
   */
  getNextAPIKey(): string | null {
    const now = Date.now();
    
    // Reset daily counters if needed
    this.resetDailyCountersIfNeeded(now);
    
    // Reset minute counters if needed
    this.resetMinuteCountersIfNeeded(now);

    // Find an available key
    const availableKey = this.findAvailableKey(now);
    
    if (!availableKey) {
      console.warn('No available API keys. All keys are rate limited or disabled.');
      return null;
    }

    // Update usage statistics
    availableKey.lastUsed = now;
    availableKey.requestCount++;
    availableKey.dailyRequestCount++;

    return availableKey.key;
  }

  /**
   * Report an error for the current API key
   */
  reportError(apiKey: string, error: any): void {
    const keyStatus = this.keys.find(k => k.key === apiKey);
    if (!keyStatus) return;

    keyStatus.errorCount++;
    
    // Check if we should disable this key
    if (keyStatus.errorCount >= this.config.maxErrorsBeforeDisable) {
      keyStatus.isActive = false;
      console.warn(`API key disabled due to too many errors: ${keyStatus.errorCount}`);
      
      // Re-enable after cooldown period
      setTimeout(() => {
        keyStatus.isActive = true;
        keyStatus.errorCount = 0;
        console.log('API key re-enabled after cooldown period');
      }, this.config.cooldownPeriod);
    }

    // Handle specific error types
    if (error?.message?.includes('quota') || error?.message?.includes('rate limit')) {
      // Temporarily disable key for rate limiting
      keyStatus.isActive = false;
      setTimeout(() => {
        keyStatus.isActive = true;
        console.log('API key re-enabled after rate limit cooldown');
      }, this.config.cooldownPeriod);
    }
  }

  /**
   * Report successful usage of an API key
   */
  reportSuccess(apiKey: string): void {
    const keyStatus = this.keys.find(k => k.key === apiKey);
    if (!keyStatus) return;

    // Reset error count on successful usage
    keyStatus.errorCount = Math.max(0, keyStatus.errorCount - 1);
  }

  /**
   * Get current status of all API keys
   */
  getStatus(): Array<{
    index: number;
    isActive: boolean;
    requestCount: number;
    dailyRequestCount: number;
    errorCount: number;
    lastUsed: string;
  }> {
    return this.keys.map((key, index) => ({
      index,
      isActive: key.isActive,
      requestCount: key.requestCount,
      dailyRequestCount: key.dailyRequestCount,
      errorCount: key.errorCount,
      lastUsed: key.lastUsed ? new Date(key.lastUsed).toISOString() : 'Never'
    }));
  }

  /**
   * Find an available API key
   */
  private findAvailableKey(now: number): APIKeyStatus | null {
    // First, try to find a key that hasn't hit any limits
    for (let i = 0; i < this.keys.length; i++) {
      const keyIndex = (this.currentKeyIndex + i) % this.keys.length;
      const key = this.keys[keyIndex];
      
      if (this.isKeyAvailable(key, now)) {
        this.currentKeyIndex = keyIndex;
        return key;
      }
    }

    return null;
  }

  /**
   * Check if a key is available for use
   */
  private isKeyAvailable(key: APIKeyStatus, now: number): boolean {
    if (!key.isActive) return false;
    
    // Check daily limit
    if (key.dailyRequestCount >= this.config.maxRequestsPerDay) {
      return false;
    }
    
    // Check minute limit
    if (key.requestCount >= this.config.maxRequestsPerMinute) {
      return false;
    }
    
    return true;
  }

  /**
   * Reset daily counters if a new day has started
   */
  private resetDailyCountersIfNeeded(now: number): void {
    this.keys.forEach(key => {
      const timeSinceLastReset = now - key.lastDailyReset;
      const oneDayInMs = 24 * 60 * 60 * 1000;
      
      if (timeSinceLastReset >= oneDayInMs) {
        key.dailyRequestCount = 0;
        key.lastDailyReset = now;
      }
    });
  }

  /**
   * Reset minute counters if a new minute has started
   */
  private resetMinuteCountersIfNeeded(now: number): void {
    this.keys.forEach(key => {
      if (now >= key.quotaResetTime) {
        key.requestCount = 0;
        key.quotaResetTime = now + 60000; // Next minute
      }
    });
  }
}

// Singleton instance
let apiKeyManager: APIKeyManager | null = null;

/**
 * Initialize the API key manager with environment variables
 */
export function initializeAPIKeyManager(): APIKeyManager {
  if (apiKeyManager) {
    return apiKeyManager;
  }

  const apiKeys = [
    process.env.GOOGLE_AI_API_KEY,
    process.env.GOOGLE_AI_API_KEY_2,
    process.env.GOOGLE_AI_API_KEY_3,
    process.env.GOOGLE_AI_API_KEY_4,
    process.env.GOOGLE_AI_API_KEY_5
  ].filter(Boolean) as string[];

  if (apiKeys.length === 0) {
    throw new Error('No Google AI API keys found in environment variables');
  }

  console.log(`Initialized API key manager with ${apiKeys.length} keys`);
  
  apiKeyManager = new APIKeyManager(apiKeys, {
    maxRequestsPerMinute: 60,
    maxRequestsPerDay: 1500,
    maxErrorsBeforeDisable: 3,
    cooldownPeriod: 120000 // 2 minutes
  });

  return apiKeyManager;
}

/**
 * Get the current API key manager instance
 */
export function getAPIKeyManager(): APIKeyManager {
  if (!apiKeyManager) {
    return initializeAPIKeyManager();
  }
  return apiKeyManager;
}

/**
 * Get the next available API key
 */
export function getNextAPIKey(): string | null {
  const manager = getAPIKeyManager();
  return manager.getNextAPIKey();
}

/**
 * Report an error for an API key
 */
export function reportAPIKeyError(apiKey: string, error: any): void {
  const manager = getAPIKeyManager();
  manager.reportError(apiKey, error);
}

/**
 * Report successful usage of an API key
 */
export function reportAPIKeySuccess(apiKey: string): void {
  const manager = getAPIKeyManager();
  manager.reportSuccess(apiKey);
}
