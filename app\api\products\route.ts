import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ProductFilters, ApiResponse, PaginatedResponse } from "@/utils/types";
import { advancedSearch, SearchOptions } from "@/lib/advanced-search";

// Helper function to map sort parameters to advanced search format
function mapSortBy(sortBy: string, sortOrder: string): 'relevance' | 'price_asc' | 'price_desc' | 'rating' | 'newest' {
  if (sortBy === "price") {
    return sortOrder === "desc" ? "price_desc" : "price_asc";
  }
  if (sortBy === "rating") return "rating";
  if (sortBy === "newest") return "newest";
  return "relevance";
}

// GET /api/products - Get products with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Check if this is an advanced search request
    const useAdvancedSearch = searchParams.get("advanced") === "true" || searchParams.get("search");

    if (useAdvancedSearch && searchParams.get("search")) {
      // Use advanced search for search queries
      const searchOptions: SearchOptions = {
        query: searchParams.get("search") || "",
        category: searchParams.get("categoryId") || undefined,
        brand: searchParams.get("brand") || undefined,
        minPrice: searchParams.get("minPrice") ? parseFloat(searchParams.get("minPrice")!) : undefined,
        maxPrice: searchParams.get("maxPrice") ? parseFloat(searchParams.get("maxPrice")!) : undefined,
        colors: searchParams.get("colors")?.split(",").filter(Boolean) || undefined,
        sizes: searchParams.get("sizes")?.split(",").filter(Boolean) || undefined,
        sortBy: mapSortBy(searchParams.get("sortBy") || "name", searchParams.get("sortOrder") || "asc"),
        limit: parseInt(searchParams.get("limit") || "12"),
        offset: (parseInt(searchParams.get("page") || "1") - 1) * parseInt(searchParams.get("limit") || "12"),
        includeInactive: false
      };

      const result = await advancedSearch(searchOptions);

      const response: ApiResponse<PaginatedResponse<any>> = {
        success: true,
        data: {
          data: result.products,
          pagination: {
            page: parseInt(searchParams.get("page") || "1"),
            limit: parseInt(searchParams.get("limit") || "12"),
            total: result.total,
            totalPages: Math.ceil(result.total / parseInt(searchParams.get("limit") || "12")),
          },
          suggestions: result.suggestions,
          filters: result.filters,
        },
      };

      return NextResponse.json(response);
    }

    // Parse query parameters for traditional filtering
    const filters: ProductFilters = {
      search: searchParams.get("search") || undefined,
      categoryId: searchParams.get("categoryId") || undefined,
      brand: searchParams.get("brand") || undefined,
      minPrice: searchParams.get("minPrice") ? parseFloat(searchParams.get("minPrice")!) : undefined,
      maxPrice: searchParams.get("maxPrice") ? parseFloat(searchParams.get("maxPrice")!) : undefined,
      sizes: searchParams.get("sizes") ? searchParams.get("sizes")!.split(",") : undefined,

      sortBy: (searchParams.get("sortBy") as any) || "name",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "asc",
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "12"),
    };

    // Build where clause
    const where: any = {
      isActive: true,
    };

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        { description: { contains: filters.search, mode: "insensitive" } },
        { brand: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    if (filters.categoryId) {
      where.categoryId = filters.categoryId;
    }

    if (filters.brand) {
      where.brand = { contains: filters.brand, mode: "insensitive" };
    }

    if (filters.minPrice || filters.maxPrice) {
      where.price = {};
      if (filters.minPrice) where.price.gte = filters.minPrice;
      if (filters.maxPrice) where.price.lte = filters.maxPrice;
    }

    if (filters.sizes && filters.sizes.length > 0) {
      where.sizes = { hasSome: filters.sizes };
    }

    // Stock status filter
    if (filters.stockStatus && filters.stockStatus.length > 0) {
      const stockConditions = [];

      if (filters.stockStatus.includes("in-stock")) {
        stockConditions.push({ stock: { gt: 5 } });
      }
      if (filters.stockStatus.includes("low-stock")) {
        stockConditions.push({
          AND: [
            { stock: { gt: 0 } },
            { stock: { lte: 5 } }
          ]
        });
      }
      if (filters.stockStatus.includes("out-of-stock")) {
        stockConditions.push({ stock: { lte: 0 } });
      }

      if (stockConditions.length > 0) {
        where.OR = where.OR ? [...where.OR, ...stockConditions] : stockConditions;
      }
    }



    // Build orderBy clause
    let orderBy: any = {};
    switch (filters.sortBy) {
      case "price":
        orderBy.price = filters.sortOrder;
        break;
      case "rating":
        orderBy.rating = filters.sortOrder;
        break;
      case "newest":
        orderBy.createdAt = "desc";
        break;
      case "stock":
        orderBy.stock = filters.sortOrder;
        break;
      case "brand":
        orderBy.brand = filters.sortOrder;
        break;
      default:
        orderBy.name = filters.sortOrder;
    }

    // Calculate pagination
    const skip = (filters.page! - 1) * filters.limit!;

    // Get total count
    const total = await prisma.product.count({ where });

    // Get products
    let products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: { reviews: true }
        }
      },
      orderBy,
      skip,
      take: filters.limit,
    });

    // Apply brand prioritization for default sorting (name asc) when no brand filter is applied
    // Brand priority: Nike first, then Adidas, Lacoste, Vans, Puma
    if (filters.sortBy === "name" && filters.sortOrder === "asc" && !filters.brand) {
      const brandPriority = {
        'Nike': 1,
        'Air Jordan': 1, // Same priority as Nike
        'Adidas': 2,
        'Lacoste': 3,
        'Vans': 4,
        'Puma': 5,
        'New Balance': 6
      };

      console.log('Applying brand prioritization. Products before sorting:', products.slice(0, 3).map(p => `${p.brand} - ${p.name}`));

      products = products.sort((a, b) => {
        const aPriority = brandPriority[a.brand as keyof typeof brandPriority] || 999;
        const bPriority = brandPriority[b.brand as keyof typeof brandPriority] || 999;

        // First sort by brand priority
        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }

        // Then sort by name alphabetically
        return a.name.localeCompare(b.name);
      });

      console.log('Products after brand prioritization:', products.slice(0, 3).map(p => `${p.brand} - ${p.name}`));
    }

    const response: ApiResponse<PaginatedResponse<typeof products[0]>> = {
      success: true,
      data: {
        data: products,
        pagination: {
          page: filters.page!,
          limit: filters.limit!,
          total,
          totalPages: Math.ceil(total / filters.limit!),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}
