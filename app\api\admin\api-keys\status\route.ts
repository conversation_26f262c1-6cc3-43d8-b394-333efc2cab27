import { NextRequest, NextResponse } from 'next/server';
import { getAPIKeyManager } from '@/utils/api-key-manager';
import { requireAdmin } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    const manager = getAPIKeyManager();
    const status = manager.getStatus();

    return NextResponse.json({
      success: true,
      data: {
        totalKeys: status.length,
        activeKeys: status.filter(k => k.isActive).length,
        keys: status.map(key => ({
          ...key,
          // Don't expose the actual API key for security
          hasKey: true
        }))
      }
    });

  } catch (error) {
    console.error('API key status error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get API key status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
