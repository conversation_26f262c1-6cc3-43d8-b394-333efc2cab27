"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { Product } from "@/utils/types";

interface ComparisonContextType {
  comparisonProducts: Product[];
  addToComparison: (product: Product) => void;
  removeFromComparison: (productId: string) => void;
  clearComparison: () => void;
  isInComparison: (productId: string) => boolean;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

export function ComparisonProvider({ children }: { children: React.ReactNode }) {
  const [comparisonProducts, setComparisonProducts] = useState<Product[]>([]);

  // Load from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem("comparisonProducts");
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        setComparisonProducts(parsed);
      } catch (error) {
        console.error("Error parsing comparison products:", error);
        localStorage.removeItem("comparisonProducts");
      }
    }
  }, []);

  // Save to localStorage whenever comparisonProducts changes
  useEffect(() => {
    localStorage.setItem("comparisonProducts", JSON.stringify(comparisonProducts));
  }, [comparisonProducts]);

  const addToComparison = (product: Product) => {
    setComparisonProducts(prev => {
      // Don't add if already exists
      if (prev.some(p => p.id === product.id)) {
        return prev;
      }
      // Limit to 4 products for comparison
      if (prev.length >= 4) {
        return [...prev.slice(1), product];
      }
      return [...prev, product];
    });
  };

  const removeFromComparison = (productId: string) => {
    setComparisonProducts(prev => prev.filter(p => p.id !== productId));
  };

  const clearComparison = () => {
    setComparisonProducts([]);
    localStorage.removeItem("comparisonProducts");
  };

  const isInComparison = (productId: string) => {
    return comparisonProducts.some(p => p.id === productId);
  };

  return (
    <ComparisonContext.Provider value={{
      comparisonProducts,
      addToComparison,
      removeFromComparison,
      clearComparison,
      isInComparison
    }}>
      {children}
    </ComparisonContext.Provider>
  );
}

export function useComparison() {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error("useComparison must be used within a ComparisonProvider");
  }
  return context;
}
