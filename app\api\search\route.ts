import { NextRequest, NextResponse } from 'next/server';
import { advancedSearch, SearchOptions } from '@/lib/advanced-search';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse search parameters
    const searchOptions: SearchOptions = {
      query: searchParams.get('q') || searchParams.get('search') || '',
      category: searchParams.get('category') || undefined,
      brand: searchParams.get('brand') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      colors: searchParams.get('colors')?.split(',').filter(Boolean) || undefined,
      sizes: searchParams.get('sizes')?.split(',').filter(Boolean) || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'relevance',
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: (parseInt(searchParams.get('page') || '1') - 1) * parseInt(searchParams.get('limit') || '20'),
      includeInactive: searchParams.get('includeInactive') === 'true'
    };

    // Perform advanced search
    const result = await advancedSearch(searchOptions);

    // Calculate pagination info
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const totalPages = Math.ceil(result.total / limit);

    return NextResponse.json({
      success: true,
      data: {
        products: result.products,
        total: result.total,
        page,
        totalPages,
        suggestions: result.suggestions,
        filters: result.filters,
        searchQuery: searchOptions.query,
        appliedFilters: {
          category: searchOptions.category,
          brand: searchOptions.brand,
          priceRange: {
            min: searchOptions.minPrice,
            max: searchOptions.maxPrice
          },
          colors: searchOptions.colors,
          sizes: searchOptions.sizes,
          sortBy: searchOptions.sortBy
        }
      }
    });
  } catch (error) {
    console.error('Advanced search error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to search products',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST endpoint for complex search queries
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const searchOptions: SearchOptions = {
      query: body.query || '',
      category: body.category,
      brand: body.brand,
      minPrice: body.minPrice,
      maxPrice: body.maxPrice,
      colors: body.colors,
      sizes: body.sizes,
      sortBy: body.sortBy || 'relevance',
      limit: body.limit || 20,
      offset: body.offset || 0,
      includeInactive: body.includeInactive || false
    };

    const result = await advancedSearch(searchOptions);

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Advanced search POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to search products',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
