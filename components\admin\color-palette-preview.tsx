"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Palette } from "lucide-react";

interface ColorPalette {
  name: string;
  description: string;
  primary: string;
  accent: string;
  primaryOklch: string;
  accentOklch: string;
  vibe: string;
}

const colorPalettes: ColorPalette[] = [
  {
    name: "Navy + Electric Blue (Current)",
    description: "Professional with energetic accent",
    primary: "#1e3a8a",
    accent: "#0ea5e9",
    primaryOklch: "oklch(0.25 0.12 250)",
    accentOklch: "oklch(0.7 0.15 200)",
    vibe: "Modern Professional"
  },
  {
    name: "Deep Purple + Magenta",
    description: "Luxury and creativity",
    primary: "#7c3aed",
    accent: "#ec4899",
    primaryOklch: "oklch(0.45 0.18 280)",
    accentOklch: "oklch(0.65 0.22 340)",
    vibe: "Premium Lifestyle"
  },
  {
    name: "Charcoal + Neon Green",
    description: "Dark sophisticated with energy",
    primary: "#1f2937",
    accent: "#10b981",
    primaryOklch: "oklch(0.2 0.02 240)",
    accentOklch: "oklch(0.7 0.15 160)",
    vibe: "Tech Innovation"
  },
  {
    name: "Navy + Orange",
    description: "Classic sporty combination",
    primary: "#1e3a8a",
    accent: "#f97316",
    primaryOklch: "oklch(0.25 0.12 250)",
    accentOklch: "oklch(0.7 0.18 50)",
    vibe: "Athletic Premium"
  },
  {
    name: "Teal + Coral",
    description: "Fresh and modern",
    primary: "#0f766e",
    accent: "#f87171",
    primaryOklch: "oklch(0.45 0.12 180)",
    accentOklch: "oklch(0.7 0.15 20)",
    vibe: "Fresh & Vibrant"
  }
];

export default function ColorPalettePreview() {
  const [selectedPalette, setSelectedPalette] = useState(0);

  const applyPalette = (palette: ColorPalette) => {
    // This would update the CSS variables
    console.log(`Applying palette: ${palette.name}`);
    console.log(`Primary: ${palette.primaryOklch}`);
    console.log(`Accent: ${palette.accentOklch}`);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
          <Palette className="h-6 w-6" />
          RIVV Color Palette Options
        </h2>
        <p className="text-gray-600">Choose a color scheme that brings life to your brand</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {colorPalettes.map((palette, index) => (
          <Card 
            key={index} 
            className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
              selectedPalette === index ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedPalette(index)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{palette.name}</CardTitle>
                {selectedPalette === index && (
                  <Check className="h-5 w-5 text-green-600" />
                )}
              </div>
              <p className="text-sm text-gray-600">{palette.description}</p>
              <Badge variant="outline" className="w-fit">
                {palette.vibe}
              </Badge>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Color Swatches */}
              <div className="flex gap-3">
                <div className="flex-1">
                  <div 
                    className="h-16 rounded-lg border"
                    style={{ backgroundColor: palette.primary }}
                  ></div>
                  <p className="text-xs text-center mt-1 font-medium">Primary</p>
                </div>
                <div className="flex-1">
                  <div 
                    className="h-16 rounded-lg border"
                    style={{ backgroundColor: palette.accent }}
                  ></div>
                  <p className="text-xs text-center mt-1 font-medium">Accent</p>
                </div>
              </div>

              {/* Sample UI Elements */}
              <div className="space-y-2">
                <Button 
                  className="w-full text-white"
                  style={{ backgroundColor: palette.primary }}
                >
                  Shop Collection
                </Button>
                <Button 
                  variant="outline"
                  className="w-full"
                  style={{ 
                    borderColor: palette.accent,
                    color: palette.accent
                  }}
                >
                  View Details
                </Button>
              </div>

              {/* Sample Text */}
              <div className="text-center">
                <h3 
                  className="font-bold text-lg"
                  style={{ color: palette.primary }}
                >
                  Unapologetically Premium
                </h3>
                <p className="text-sm text-gray-600">
                  RIVV Premium Sneakers
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Apply Button */}
      <div className="text-center">
        <Button 
          size="lg"
          onClick={() => applyPalette(colorPalettes[selectedPalette])}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
        >
          Apply {colorPalettes[selectedPalette].name}
        </Button>
      </div>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Selected Palette Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Primary Color</h4>
              <code className="text-sm bg-gray-100 p-2 rounded block">
                {colorPalettes[selectedPalette].primaryOklch}
              </code>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Accent Color</h4>
              <code className="text-sm bg-gray-100 p-2 rounded block">
                {colorPalettes[selectedPalette].accentOklch}
              </code>
            </div>
          </div>
          <div className="mt-4">
            <h4 className="font-semibold mb-2">Brand Personality</h4>
            <p className="text-gray-600">{colorPalettes[selectedPalette].description}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
