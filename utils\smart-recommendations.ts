/**
 * Smart product recommendation system
 */

import { Product } from "@/utils/types";

export interface RecommendationContext {
  currentProduct?: Product;
  userBehavior?: {
    viewedProducts: string[];
    searchHistory: string[];
    cartItems: string[];
    favoriteProducts: string[];
  };
  userPreferences?: {
    preferredBrands: string[];
    preferredPriceRange: [number, number];
    preferredSizes: string[];
    preferredColors: string[];
  };
}

export interface RecommendationResult {
  products: Product[];
  reason: string;
  confidence: number;
}

/**
 * Generate smart product recommendations based on context
 */
export function generateRecommendations(
  allProducts: Product[],
  context: RecommendationContext,
  maxRecommendations: number = 8
): RecommendationResult[] {
  const recommendations: RecommendationResult[] = [];
  
  // Filter out current product and inactive products
  const availableProducts = allProducts.filter(p => 
    p.isActive && 
    p.stock > 0 && 
    p.id !== context.currentProduct?.id
  );

  // 1. Similar products (same brand/category)
  if (context.currentProduct) {
    const similarProducts = availableProducts
      .filter(p => 
        p.brand === context.currentProduct!.brand ||
        p.categoryId === context.currentProduct!.categoryId
      )
      .filter(p => p.id !== context.currentProduct!.id)
      .sort((a, b) => {
        // Prioritize same brand, then same category
        const aScore = (a.brand === context.currentProduct!.brand ? 2 : 0) +
                      (a.categoryId === context.currentProduct!.categoryId ? 1 : 0);
        const bScore = (b.brand === context.currentProduct!.brand ? 2 : 0) +
                      (b.categoryId === context.currentProduct!.categoryId ? 1 : 0);
        return bScore - aScore;
      })
      .slice(0, 4);

    if (similarProducts.length > 0) {
      recommendations.push({
        products: similarProducts,
        reason: `More from ${context.currentProduct.brand}`,
        confidence: 85
      });
    }
  }

  // 2. Price-based recommendations
  if (context.currentProduct) {
    const currentPrice = context.currentProduct.discountedPrice || context.currentProduct.price;
    const priceRange = currentPrice * 0.3; // ±30% price range
    
    const similarPriceProducts = availableProducts
      .filter(p => {
        const price = p.discountedPrice || p.price;
        return Math.abs(price - currentPrice) <= priceRange;
      })
      .sort((a, b) => {
        const aPrice = a.discountedPrice || a.price;
        const bPrice = b.discountedPrice || b.price;
        return Math.abs(aPrice - currentPrice) - Math.abs(bPrice - currentPrice);
      })
      .slice(0, 4);

    if (similarPriceProducts.length > 0) {
      recommendations.push({
        products: similarPriceProducts,
        reason: "Similar price range",
        confidence: 70
      });
    }
  }

  // 3. User behavior-based recommendations
  if (context.userBehavior?.viewedProducts.length) {
    const viewedProductIds = context.userBehavior.viewedProducts.slice(0, 5);
    const viewedProducts = allProducts.filter(p => viewedProductIds.includes(p.id));
    
    // Find products with similar characteristics
    const behaviorBasedProducts = availableProducts
      .filter(p => {
        return viewedProducts.some(viewed => 
          p.brand === viewed.brand || 
          p.categoryId === viewed.categoryId ||
          p.colors.some(color => viewed.colors.includes(color))
        );
      })
      .sort((a, b) => {
        // Score based on similarity to viewed products
        const aScore = viewedProducts.reduce((score, viewed) => {
          return score + 
            (a.brand === viewed.brand ? 3 : 0) +
            (a.categoryId === viewed.categoryId ? 2 : 0) +
            (a.colors.some(color => viewed.colors.includes(color)) ? 1 : 0);
        }, 0);
        
        const bScore = viewedProducts.reduce((score, viewed) => {
          return score + 
            (b.brand === viewed.brand ? 3 : 0) +
            (b.categoryId === viewed.categoryId ? 2 : 0) +
            (b.colors.some(color => viewed.colors.includes(color)) ? 1 : 0);
        }, 0);
        
        return bScore - aScore;
      })
      .slice(0, 4);

    if (behaviorBasedProducts.length > 0) {
      recommendations.push({
        products: behaviorBasedProducts,
        reason: "Based on your browsing",
        confidence: 75
      });
    }
  }

  // 4. Trending/Popular products (high rating, recent)
  const trendingProducts = availableProducts
    .filter(p => p.rating >= 4.0)
    .sort((a, b) => {
      // Combine rating and recency score
      const aScore = a.rating * 0.7 + (new Date(a.createdAt).getTime() / 1000000000) * 0.3;
      const bScore = b.rating * 0.7 + (new Date(b.createdAt).getTime() / 1000000000) * 0.3;
      return bScore - aScore;
    })
    .slice(0, 4);

  if (trendingProducts.length > 0) {
    recommendations.push({
      products: trendingProducts,
      reason: "Trending now",
      confidence: 60
    });
  }

  // 5. Brand priority recommendations (Nike, Adidas, etc.)
  const priorityBrands = ['Nike', 'Adidas', 'Jordan', 'Lacoste', 'Vans', 'Puma'];
  const brandPriorityProducts = availableProducts
    .filter(p => priorityBrands.includes(p.brand))
    .sort((a, b) => {
      const aPriority = priorityBrands.indexOf(a.brand);
      const bPriority = priorityBrands.indexOf(b.brand);
      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }
      return b.rating - a.rating;
    })
    .slice(0, 4);

  if (brandPriorityProducts.length > 0) {
    recommendations.push({
      products: brandPriorityProducts,
      reason: "Premium brands",
      confidence: 65
    });
  }

  // 6. Sale/Discounted products
  const saleProducts = availableProducts
    .filter(p => p.discountedPrice && p.discountedPrice < p.price)
    .sort((a, b) => {
      const aDiscount = ((a.price - (a.discountedPrice || a.price)) / a.price) * 100;
      const bDiscount = ((b.price - (b.discountedPrice || b.price)) / b.price) * 100;
      return bDiscount - aDiscount;
    })
    .slice(0, 4);

  if (saleProducts.length > 0) {
    recommendations.push({
      products: saleProducts,
      reason: "Special offers",
      confidence: 80
    });
  }

  // Remove duplicates and limit results
  const uniqueRecommendations = removeDuplicateProducts(recommendations);
  
  return uniqueRecommendations
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, Math.ceil(maxRecommendations / 4));
}

/**
 * Remove duplicate products across recommendation sets
 */
function removeDuplicateProducts(recommendations: RecommendationResult[]): RecommendationResult[] {
  const seenProductIds = new Set<string>();
  
  return recommendations.map(rec => ({
    ...rec,
    products: rec.products.filter(product => {
      if (seenProductIds.has(product.id)) {
        return false;
      }
      seenProductIds.add(product.id);
      return true;
    })
  })).filter(rec => rec.products.length > 0);
}

/**
 * Get smart search suggestions based on user context
 */
export function getSmartSearchSuggestions(
  query: string,
  context: RecommendationContext
): string[] {
  const suggestions: string[] = [];
  
  // Add brand-based suggestions
  if (context.userPreferences?.preferredBrands.length) {
    context.userPreferences.preferredBrands.forEach(brand => {
      if (brand.toLowerCase().includes(query.toLowerCase())) {
        suggestions.push(brand);
      }
    });
  }
  
  // Add search history suggestions
  if (context.userBehavior?.searchHistory.length) {
    context.userBehavior.searchHistory.forEach(search => {
      if (search.toLowerCase().includes(query.toLowerCase()) && !suggestions.includes(search)) {
        suggestions.push(search);
      }
    });
  }
  
  // Add category-based suggestions
  const categories = ['sneakers', 'boots', 'sandals', 'running shoes', 'basketball shoes'];
  categories.forEach(category => {
    if (category.toLowerCase().includes(query.toLowerCase()) && !suggestions.includes(category)) {
      suggestions.push(category);
    }
  });
  
  return suggestions.slice(0, 5);
}

/**
 * Calculate product compatibility score for recommendations
 */
export function calculateCompatibilityScore(
  product: Product,
  context: RecommendationContext
): number {
  let score = 0;
  
  // Brand preference
  if (context.userPreferences?.preferredBrands.includes(product.brand)) {
    score += 30;
  }
  
  // Price range preference
  if (context.userPreferences?.preferredPriceRange) {
    const [minPrice, maxPrice] = context.userPreferences.preferredPriceRange;
    const productPrice = product.discountedPrice || product.price;
    if (productPrice >= minPrice && productPrice <= maxPrice) {
      score += 25;
    }
  }
  
  // Size availability
  if (context.userPreferences?.preferredSizes.length) {
    const hasPreferredSize = context.userPreferences.preferredSizes.some(size => 
      product.sizes.includes(size)
    );
    if (hasPreferredSize) {
      score += 20;
    }
  }
  
  // Color preference
  if (context.userPreferences?.preferredColors.length) {
    const hasPreferredColor = context.userPreferences.preferredColors.some(color => 
      product.colors.some(productColor => 
        productColor.toLowerCase().includes(color.toLowerCase())
      )
    );
    if (hasPreferredColor) {
      score += 15;
    }
  }
  
  // Stock availability
  if (product.stock > 0) {
    score += 10;
  }
  
  return Math.min(score, 100);
}
