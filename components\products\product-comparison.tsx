"use client";

import { useComparison } from "@/contexts/comparison-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Scale, Star } from "lucide-react";
import { formatPrice, getEffectivePrice, isProductOnSale } from "@/lib/product-utils";
import Link from "next/link";

export default function ProductComparison() {
  const { comparisonProducts, removeFromComparison, clearComparison } = useComparison();

  if (comparisonProducts.length === 0) {
    return null;
  }

  return (
    <Card className="mt-8">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Scale className="h-5 w-5" />
            Product Comparison ({comparisonProducts.length})
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={clearComparison}
            className="text-red-600 hover:text-red-700"
          >
            <X className="h-4 w-4" />
            Clear All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-4 font-medium">Product</th>
                {comparisonProducts.map((product) => (
                  <th key={product.id} className="text-center p-4 min-w-[200px]">
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFromComparison(product.id)}
                        className="absolute -top-2 -right-2 h-6 w-6 p-0 text-red-600 hover:text-red-700"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <Link href={`/products/${product.id}`}>
                        <img
                          src={product.images[0] || "/placeholder-product.jpg"}
                          alt={product.name}
                          className="w-24 h-24 object-cover rounded-lg mx-auto mb-2"
                        />
                        <h3 className="font-medium text-sm line-clamp-2">{product.name}</h3>
                        <p className="text-xs text-gray-500">{product.brand}</p>
                      </Link>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {/* Price */}
              <tr className="border-b">
                <td className="p-4 font-medium">Price</td>
                {comparisonProducts.map((product) => {
                  const effectivePrice = getEffectivePrice(product);
                  const isOnSale = isProductOnSale(product);
                  return (
                    <td key={product.id} className="p-4 text-center">
                      <div className="space-y-1">
                        <div className="font-bold text-lg">{formatPrice(effectivePrice)}</div>
                        {isOnSale && (
                          <div className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </div>
                        )}
                      </div>
                    </td>
                  );
                })}
              </tr>

              {/* Rating */}
              <tr className="border-b">
                <td className="p-4 font-medium">Rating</td>
                {comparisonProducts.map((product) => (
                  <td key={product.id} className="p-4 text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{product.rating.toFixed(1)}</span>
                      <span className="text-sm text-gray-500">({product.reviewCount})</span>
                    </div>
                  </td>
                ))}
              </tr>

              {/* Stock */}
              <tr className="border-b">
                <td className="p-4 font-medium">Stock</td>
                {comparisonProducts.map((product) => (
                  <td key={product.id} className="p-4 text-center">
                    <Badge variant={product.stock > 0 ? "default" : "destructive"}>
                      {product.stock > 0 ? `${product.stock} in stock` : "Out of stock"}
                    </Badge>
                  </td>
                ))}
              </tr>

              {/* Sizes */}
              <tr className="border-b">
                <td className="p-4 font-medium">Available Sizes</td>
                {comparisonProducts.map((product) => (
                  <td key={product.id} className="p-4 text-center">
                    <div className="flex flex-wrap gap-1 justify-center">
                      {product.sizes.slice(0, 4).map((size) => (
                        <Badge key={size} variant="outline" className="text-xs">
                          {size}
                        </Badge>
                      ))}
                      {product.sizes.length > 4 && (
                        <Badge variant="outline" className="text-xs">
                          +{product.sizes.length - 4}
                        </Badge>
                      )}
                    </div>
                  </td>
                ))}
              </tr>

              {/* Colors */}
              <tr className="border-b">
                <td className="p-4 font-medium">Available Colors</td>
                {comparisonProducts.map((product) => (
                  <td key={product.id} className="p-4 text-center">
                    <div className="flex flex-wrap gap-1 justify-center">
                      {product.colors && product.colors.length > 0 ? (
                        <>
                          {product.colors.slice(0, 3).map((color) => (
                            <Badge key={color} variant="secondary" className="text-xs">
                              {color}
                            </Badge>
                          ))}
                          {product.colors.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{product.colors.length - 3}
                            </Badge>
                          )}
                        </>
                      ) : (
                        <span className="text-sm text-gray-500">N/A</span>
                      )}
                    </div>
                  </td>
                ))}
              </tr>

              {/* Actions */}
              <tr>
                <td className="p-4 font-medium">Actions</td>
                {comparisonProducts.map((product) => (
                  <td key={product.id} className="p-4 text-center">
                    <div className="space-y-2">
                      <Link href={`/products/${product.id}`}>
                        <Button size="sm" className="w-full">
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
