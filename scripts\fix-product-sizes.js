// Script to fix product sizes based on category and product type
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🔧 Fixing product sizes based on categories...');
  
  try {
    // Get all products with their categories
    const products = await prisma.product.findMany({
      include: {
        category: true,
      },
      orderBy: { name: 'asc' },
    });

    console.log(`Found ${products.length} products to analyze`);

    let updatedCount = 0;
    const updates = [];

    for (const product of products) {
      const name = product.name.toLowerCase();
      const categoryName = product.category.name.toLowerCase();
      
      let newSizes = [];
      let reason = "";

      // Determine correct sizes based on category and product name
      if (categoryName.includes("sneaker") || categoryName.includes("footwear") || 
          name.includes("shoe") || name.includes("sneaker") || name.includes("boot") || 
          name.includes("sandal") || name.includes("slipper")) {
        
        // Shoe sizes - detect gender for appropriate range
        const womenKeywords = ["women", "womens", "woman", "ladies", "female", "girl", "girls"];
        const menKeywords = ["men", "mens", "man", "male", "boy", "boys"];

        if (womenKeywords.some(keyword => name.includes(keyword))) {
          newSizes = ["3", "4", "5", "6", "7"]; // Women's shoe sizes
          reason = "Women's shoe sizes";
        } else if (menKeywords.some(keyword => name.includes(keyword))) {
          newSizes = ["5", "6", "7", "8", "9", "10", "11", "12"]; // Men's shoe sizes
          reason = "Men's shoe sizes";
        } else {
          newSizes = ["3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]; // Unisex shoe sizes
          reason = "Unisex shoe sizes";
        }
      } else if (categoryName.includes("fashionwear") || categoryName.includes("clothing") ||
                 name.includes("shirt") || name.includes("dress") || name.includes("pants") || 
                 name.includes("jacket") || name.includes("hoodie") || name.includes("top") ||
                 name.includes("blouse") || name.includes("skirt") || name.includes("shorts") ||
                 name.includes("sweater") || name.includes("coat")) {
        
        // Clothing sizes
        newSizes = ["XS", "S", "M", "L", "XL", "XXL"];
        reason = "Clothing sizes";
      } else if (categoryName.includes("accessories") || categoryName.includes("accessory") ||
                 name.includes("bag") || name.includes("handbag") || name.includes("backpack") ||
                 name.includes("purse") || name.includes("wallet") || name.includes("belt") ||
                 name.includes("watch") || name.includes("jewelry")) {
        
        // Accessories - most don't need sizes, but some do
        if (name.includes("belt") || name.includes("ring") || name.includes("bracelet")) {
          newSizes = ["XS", "S", "M", "L", "XL"]; // Size-dependent accessories
          reason = "Size-dependent accessory";
        } else {
          newSizes = ["One Size"]; // Most accessories are one size
          reason = "One size accessory";
        }
      } else if (categoryName.includes("headwear") ||
                 name.includes("cap") || name.includes("hat") || name.includes("beanie")) {
        
        // Headwear sizes
        newSizes = ["S", "M", "L", "XL"];
        reason = "Headwear sizes";
      } else if (categoryName.includes("intimate") ||
                 name.includes("underwear") || name.includes("bra") || name.includes("panties") ||
                 name.includes("boxers") || name.includes("briefs")) {
        
        // Intimate apparel sizes
        newSizes = ["XS", "S", "M", "L", "XL", "XXL"];
        reason = "Intimate apparel sizes";
      } else {
        // Default fallback - assume clothing sizes
        newSizes = ["XS", "S", "M", "L", "XL"];
        reason = "Default clothing sizes";
      }

      // Check if sizes need updating
      const currentSizes = product.sizes || [];
      const sizesNeedUpdate = JSON.stringify(currentSizes.sort()) !== JSON.stringify(newSizes.sort());

      if (sizesNeedUpdate) {
        updates.push({
          id: product.id,
          name: product.name,
          category: product.category.name,
          currentSizes: currentSizes,
          newSizes: newSizes,
          reason: reason
        });
      }
    }

    console.log(`\n📊 Found ${updates.length} products that need size updates:`);
    
    if (updates.length === 0) {
      console.log('✅ All products already have correct sizes!');
      return;
    }

    // Group by category for better display
    const groupedUpdates = {};
    updates.forEach(update => {
      if (!groupedUpdates[update.category]) {
        groupedUpdates[update.category] = [];
      }
      groupedUpdates[update.category].push(update);
    });

    // Display what will be updated
    Object.entries(groupedUpdates).forEach(([category, products]) => {
      console.log(`\n🏷️  ${category}:`);
      products.forEach(product => {
        console.log(`   • ${product.name}`);
        console.log(`     Current: [${product.currentSizes.join(', ')}]`);
        console.log(`     New: [${product.newSizes.join(', ')}] (${product.reason})`);
      });
    });

    console.log('\n🔧 Applying updates...');
    
    // Apply updates
    for (const update of updates) {
      try {
        await prisma.product.update({
          where: { id: update.id },
          data: { sizes: update.newSizes },
        });
        console.log(`✅ Updated "${update.name}"`);
        updatedCount++;
      } catch (error) {
        console.error(`❌ Failed to update "${update.name}":`, error.message);
      }
    }
    
    console.log(`\n🎉 Successfully updated ${updatedCount} products!`);

  } catch (error) {
    console.error('Error fixing product sizes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
