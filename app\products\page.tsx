"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import NavBar from "@/components/navbar";
import ProductFilters from "@/components/products/product-filters";
import ProductGrid from "@/components/products/product-grid";
import ProductSort from "@/components/products/product-sort";
import ReferralBanner from "@/components/referral/referral-banner";
import { useSession } from "@/lib/auth-client";
import {
  Product,
  ProductFilters as ProductFiltersType,
  PaginatedResponse,
  User,
} from "@/utils/types";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Filter, X } from "lucide-react";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import NavBarSkeleton from "@/components/navBarSkeleton";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRef } from "react";

function ProductsPageContent() {
  const { data: session, isPending } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedTab, setSelectedTab] = useState<string>("sneakers");
  const [fade, setFade] = useState<boolean>(false);
  const gridRef = useRef<HTMLDivElement>(null);

  // Fetch categories to get IDs for tabs
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };
    fetchCategories();
  }, []);

  // Define category tabs
  const categoryTabs = [
    { label: "Sneakers", name: "Sneakers" },
    { label: "Fashionwear", name: "Fashionwear" },
    { label: "Accessories", name: "Accessories" },
    { label: "Headwear", name: "Headwear" },
    { label: "Intimates", name: "Intimates" },
  ];

  // Parse filters from URL
  const getFiltersFromUrl = (): ProductFiltersType => {
    return {
      search: searchParams.get("search") || undefined,
      categoryId: searchParams.get("categoryId") || undefined,
      brand: searchParams.get("brand") || undefined,
      minPrice: searchParams.get("minPrice")
        ? parseFloat(searchParams.get("minPrice")!)
        : undefined,
      maxPrice: searchParams.get("maxPrice")
        ? parseFloat(searchParams.get("maxPrice")!)
        : undefined,
      sizes: searchParams.get("sizes")
        ? searchParams.get("sizes")!.split(",")
        : undefined,

      sortBy: (searchParams.get("sortBy") as any) || "name",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "asc",
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "12"),
    };
  };

  const [filters, setFilters] = useState<ProductFiltersType>(
    getFiltersFromUrl()
  );

  // Update URL when filters change
  const updateUrl = (newFilters: ProductFiltersType) => {
    const params = new URLSearchParams();

    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            params.set(key, value.join(","));
          }
        } else {
          params.set(key, value.toString());
        }
      }
    });

    router.push(`/products?${params.toString()}`);
  };

  // Fetch products
  const fetchProducts = async (currentFilters: ProductFiltersType) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            if (value.length > 0) {
              params.set(key, value.join(","));
            }
          } else {
            params.set(key, value.toString());
          }
        }
      });

      const response = await fetch(`/api/products?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setProducts(result.data.data);
        setPagination(result.data.pagination);
      } else {
        setError(result.error || "Failed to fetch products");
      }
    } catch (err) {
      setError("Failed to fetch products");
      console.error("Error fetching products:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: ProductFiltersType) => {
    const updatedFilters = { ...newFilters, page: 1 }; // Reset to first page
    setFilters(updatedFilters);
    updateUrl(updatedFilters);
  };

  // Handle sort changes
  const handleSortChange = (
    sortBy: "name" | "price" | "rating" | "newest" | "brand" | "stock",
    sortOrder: "asc" | "desc"
  ) => {
    const updatedFilters = { ...filters, sortBy, sortOrder, page: 1 };
    setFilters(updatedFilters);
    updateUrl(updatedFilters);
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    const updatedFilters = { ...filters, page };
    setFilters(updatedFilters);
    updateUrl(updatedFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters: ProductFiltersType = {
      sortBy: "name",
      sortOrder: "asc",
      page: 1,
      limit: 12,
    };
    setFilters(clearedFilters);
    updateUrl(clearedFilters);
  };

  // Helper to get category ID by name
  const getCategoryIdByName = (name: string) => {
    const category = categories.find(c => c.name.toLowerCase() === name.toLowerCase());
    return category ? category.id : undefined;
  };

  // Helper to get category ID by tab value
  const getCategoryIdByTab = (tabValue: string) => {
    const tab = categoryTabs.find(t => t.label.toLowerCase() === tabValue);
    if (!tab) return undefined;
    const category = categories.find(c => c.name.toLowerCase() === tab.name.toLowerCase());
    return category ? category.id : undefined;
  };

  // Handle tab change with animation
  const handleTabChange = (value: string) => {
    setFade(true);
    setTimeout(() => {
      setSelectedTab(value);
      const categoryId = getCategoryIdByTab(value);
      handleFiltersChange({ ...filters, categoryId });
      setFade(false);
    }, 200); // 200ms fade out
  };

  // On mount or filters change, sync selectedTab with filters
  useEffect(() => {
    if (filters.categoryId) {
      const match = categories.find(c => c.id === filters.categoryId);
      if (match) {
        setSelectedTab(match.name.toLowerCase());
      }
    } else {
      setSelectedTab("sneakers");
    }
  }, [filters.categoryId, categories]);

  // Fetch products when filters change
  useEffect(() => {
    fetchProducts(filters);
  }, [filters]);

  // Update filters when URL changes
  useEffect(() => {
    setFilters(getFiltersFromUrl());
  }, [searchParams]);

  // if (!session) {
  //   return (
  //     <div className="w-full h-screen flex items-center justify-center">
  //       <SpinnerCircle4 />
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen bg-gray-50">
      {isPending || !session ? (
        <NavBarSkeleton loading={isPending} user={null} />
      ) : (
        <NavBar user={session.user as User} />
      )}

      <div className="container mx-auto px-4 py-6">
        {/* Referral Banner */}
        <ReferralBanner />

        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Products</h1>
            <p className="text-gray-600">
              {pagination.total} {pagination.total === 1 ? "product" : "products"} found
            </p>
          </div>
          <div className="flex items-center gap-4 mt-4 md:mt-0">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="md:hidden border-2 border-blue-500 text-blue-700 font-semibold shadow-sm animate-pulse"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
            <ProductSort
              sortBy={filters.sortBy || "name"}
              sortOrder={filters.sortOrder || "asc"}
              onSortChange={handleSortChange}
            />
            {(filters.search ||
              filters.categoryId ||
              filters.brand ||
              filters.minPrice ||
              filters.maxPrice ||
              (filters.sizes && filters.sizes.length > 0) ||
              false) && (
              <Button variant="ghost" onClick={clearFilters}>
                <X className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {/* Filter Instruction */}
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-900 font-medium text-center shadow-sm">
          <span>Use the filters on the left (or tap the <b>Filters</b> button on mobile) to quickly find your perfect product!</span>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedTab} onValueChange={handleTabChange} className="mb-6">
          <div className="w-full overflow-x-auto whitespace-nowrap -mx-4 px-4 scrollbar-hide mb-4">
            <TabsList className="inline-flex min-w-max">
              {categoryTabs.map(tab => (
                <TabsTrigger
                  key={tab.label}
                  value={tab.label.toLowerCase()}
                  className={selectedTab === tab.label.toLowerCase() ? "bg-blue-100 text-blue-800 font-bold border-b-2 border-blue-500" : ""}
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        </Tabs>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div className={`lg:w-64 ${showFilters ? "block" : "hidden lg:block"}`}>
            <ProductFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              products={products}
            />
          </div>
          {/* Products Grid with fade animation */}
          <div className="flex-1">
            <div
              ref={gridRef}
              className={`transition-opacity duration-300 ${fade ? "opacity-0" : "opacity-100"}`}
            >
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <SpinnerCircle4 />
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <p className="text-red-600 mb-4">{error}</p>
                  <Button onClick={() => fetchProducts(filters)}>
                    Try Again
                  </Button>
                </div>
              ) : (
                <ProductGrid
                  products={products}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProductsPage() {
  return (
    <Suspense
      fallback={
        <div className="w-full h-screen flex justify-center items-center">
          <SpinnerCircle4 />
        </div>
      }
    >
      <ProductsPageContent />
    </Suspense>
  );
}
