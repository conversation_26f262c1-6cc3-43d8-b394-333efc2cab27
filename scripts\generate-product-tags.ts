/**
 * <PERSON><PERSON><PERSON> to generate tags and keywords for existing products
 */

import { PrismaClient } from '@prisma/client';
import { 
  generateProductTags, 
  generateSearchTerms, 
  calculateSearchScore,
  extractKeywordsFromName 
} from '../lib/search-enhancement';

const prisma = new PrismaClient();

async function generateTagsForAllProducts() {
  console.log('🔍 Starting product analysis and tag generation...');
  
  try {
    // Get all products with their categories
    const products = await prisma.product.findMany({
      include: {
        category: true
      }
    });
    
    console.log(`📦 Found ${products.length} products to analyze`);
    
    let updatedCount = 0;
    
    for (const product of products) {
      console.log(`\n🏷️  Analyzing: ${product.name}`);
      
      // Generate tags
      const tags = generateProductTags({
        name: product.name,
        brand: product.brand,
        category: product.category,
        colors: product.colors,
        sizes: product.sizes,
        description: product.description || undefined
      });
      
      // Generate keywords
      const keywords = extractKeywordsFromName(product.name);
      
      // Generate search terms
      const searchTerms = generateSearchTerms({
        name: product.name,
        brand: product.brand,
        category: product.category,
        colors: product.colors,
        sizes: product.sizes,
        description: product.description || undefined
      });
      
      // Calculate search score
      const searchScore = calculateSearchScore({
        name: product.name,
        brand: product.brand,
        rating: product.rating,
        reviewCount: product.reviewCount,
        stock: product.stock,
        isActive: product.isActive
      });
      
      console.log(`   📋 Tags: ${tags.join(', ')}`);
      console.log(`   🔑 Keywords: ${keywords.slice(0, 10).join(', ')}${keywords.length > 10 ? '...' : ''}`);
      console.log(`   🔍 Search Terms: ${searchTerms.slice(0, 10).join(', ')}${searchTerms.length > 10 ? '...' : ''}`);
      console.log(`   ⭐ Search Score: ${searchScore}`);
      
      // Update the product with generated data
      await prisma.product.update({
        where: { id: product.id },
        data: {
          tags,
          keywords,
          searchTerms,
          searchScore,
          tagsUpdatedAt: new Date(),
          tagsUpdatedBy: 'system-auto-generation'
        }
      });
      
      updatedCount++;
    }
    
    console.log(`\n✅ Successfully updated ${updatedCount} products with tags and keywords`);
    
    // Generate summary statistics
    const tagStats = await generateTagStatistics();
    console.log('\n📊 Tag Statistics:');
    console.log(tagStats);
    
  } catch (error) {
    console.error('❌ Error generating tags:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function generateTagStatistics() {
  const products = await prisma.product.findMany({
    select: {
      tags: true,
      keywords: true,
      searchTerms: true,
      brand: true,
      category: {
        select: { name: true }
      }
    }
  });
  
  const tagCounts: Record<string, number> = {};
  const brandCounts: Record<string, number> = {};
  const categoryCounts: Record<string, number> = {};
  
  products.forEach(product => {
    // Count tags
    product.tags.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1;
    });
    
    // Count brands
    brandCounts[product.brand] = (brandCounts[product.brand] || 0) + 1;
    
    // Count categories
    if (product.category) {
      categoryCounts[product.category.name] = (categoryCounts[product.category.name] || 0) + 1;
    }
  });
  
  return {
    totalProducts: products.length,
    uniqueTags: Object.keys(tagCounts).length,
    mostCommonTags: Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([tag, count]) => `${tag} (${count})`),
    brandDistribution: Object.entries(brandCounts)
      .sort(([,a], [,b]) => b - a)
      .map(([brand, count]) => `${brand}: ${count}`),
    categoryDistribution: Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)
      .map(([category, count]) => `${category}: ${count}`)
  };
}

// Run the script
if (require.main === module) {
  generateTagsForAllProducts()
    .then(() => {
      console.log('\n🎉 Tag generation completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { generateTagsForAllProducts, generateTagStatistics };
