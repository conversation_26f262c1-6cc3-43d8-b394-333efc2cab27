import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Helper function to require admin access
async function requireAdmin() {
  const user = await getCurrentUser();
  if (!user || user.role !== "ADMIN") {
    throw new Error("Admin access required");
  }
  return user;
}

// POST /api/admin/products/[id]/remove-image - Remove a specific product image
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await context.params;
    
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const { imageIndex } = body;

    // Validate required fields
    if (typeof imageIndex !== "number" || imageIndex < 0) {
      return NextResponse.json(
        { success: false, error: "Valid image index is required" },
        { status: 400 }
      );
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, images: true, name: true },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Validate image index
    if (imageIndex >= existingProduct.images.length) {
      return NextResponse.json(
        { success: false, error: "Image index out of range" },
        { status: 400 }
      );
    }

    // Check if this is the last image
    if (existingProduct.images.length <= 1) {
      return NextResponse.json(
        { success: false, error: "Cannot remove the last image. Products must have at least one image." },
        { status: 400 }
      );
    }

    // Create new images array without the removed image
    const newImages = existingProduct.images.filter((_, index) => index !== imageIndex);
    const removedImageUrl = existingProduct.images[imageIndex];

    // Update the product with the new images array
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        images: newImages,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        images: true,
        updatedAt: true,
      },
    });

    const response: ApiResponse<typeof updatedProduct> = {
      success: true,
      data: updatedProduct,
      message: `Image ${imageIndex + 1} removed successfully`,
    };

    console.log(`[ADMIN] Image removed from product ${existingProduct.name} (${productId}): Position ${imageIndex + 1}, URL: ${removedImageUrl}`);

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error removing product image:", error);
    
    if (error instanceof Error && error.message === "Admin access required") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to remove image" },
      { status: 500 }
    );
  }
}
