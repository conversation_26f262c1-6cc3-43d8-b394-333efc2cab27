import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// GET /api/referral/validate - Validate referral code and get discount info
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const refCode = searchParams.get("ref");
    const discountCode = searchParams.get("discount");

    if (!refCode || !discountCode) {
      return NextResponse.json(
        { success: false, error: "Referral code and discount code are required" },
        { status: 400 }
      );
    }

    // Find the sales partner
    const partner = await prisma.salesPartner.findUnique({
      where: { 
        referralCode: refCode.toUpperCase(),
        isActive: true 
      },
      select: {
        id: true,
        name: true,
        surname: true,
        referralCode: true,
        discountCode: true,
        discountAmount: true,
        isActive: true
      }
    });

    if (!partner) {
      return NextResponse.json(
        { success: false, error: "Invalid referral code" },
        { status: 404 }
      );
    }

    // Verify the discount code matches
    if (partner.discountCode !== discountCode.toUpperCase()) {
      return NextResponse.json(
        { success: false, error: "Invalid discount code for this referral" },
        { status: 400 }
      );
    }

    const response: ApiResponse<{
      partnerId: string;
      partnerName: string;
      referralCode: string;
      discountCode: string;
      discountAmount: number;
      discountType: string;
    }> = {
      success: true,
      data: {
        partnerId: partner.id,
        partnerName: `${partner.name} ${partner.surname}`,
        referralCode: partner.referralCode,
        discountCode: partner.discountCode,
        discountAmount: partner.discountAmount,
        discountType: 'FIXED_AMOUNT'
      },
      message: "Referral validated successfully"
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error validating referral:", error);
    return NextResponse.json(
      { success: false, error: "Failed to validate referral" },
      { status: 500 }
    );
  }
}
