import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-utils";

// POST /api/admin/combo-deals/[id]/generate-image - Generate combo deal image
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin();
    const { id } = await context.params;

    // Get combo deal with products
    const combo = await prisma.comboDeal.findUnique({
      where: { id },
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                discountedPrice: true,
                images: true,
              },
            },
          },
        },
      },
    });

    if (!combo) {
      return NextResponse.json(
        { success: false, error: "Combo deal not found" },
        { status: 404 }
      );
    }

    // Prepare product data for image generation
    const products = combo.comboProducts.map((cp) => ({
      url: cp.product.images[0] || "/placeholder-product.png",
      name: cp.product.name,
      brand: cp.product.brand,
      price: cp.product.discountedPrice || cp.product.price,
    }));

    // Return the data needed for client-side image generation
    return NextResponse.json({
      success: true,
      data: {
        comboId: combo.id,
        comboName: combo.name,
        originalPrice: combo.originalPrice,
        comboPrice: combo.comboPrice,
        products,
      },
      message: "Combo data prepared for image generation",
    });
  } catch (error) {
    console.error("Error preparing combo image generation:", error);
    
    if (error instanceof Error && error.message === "Admin access required") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to prepare image generation" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/combo-deals/[id]/generate-image - Update combo with generated image URL
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin();
    const { id } = await context.params;
    const body = await request.json();
    const { imageUrl } = body;

    if (!imageUrl) {
      return NextResponse.json(
        { success: false, error: "Image URL is required" },
        { status: 400 }
      );
    }

    // Update combo deal with generated image
    const updatedCombo = await prisma.comboDeal.update({
      where: { id },
      data: { comboImage: imageUrl },
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                images: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedCombo,
      message: "Combo image updated successfully",
    });
  } catch (error) {
    console.error("Error updating combo image:", error);
    
    if (error instanceof Error && error.message === "Admin access required") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to update combo image" },
      { status: 500 }
    );
  }
}
