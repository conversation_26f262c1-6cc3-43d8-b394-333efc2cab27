"use client";

import Link from "next/link";
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram } from "lucide-react";

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-2xl font-bold">RIVV Premium Sneakers</h3>
              <Heart className="h-5 w-5 text-red-500" />
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Your trusted partner for premium footwear and fashion accessories in Lesotho. 
              Quality products, exceptional service, and fast delivery.
            </p>
            <div className="flex space-x-4">
              <a href="https://www.facebook.com/profile.php?id=61577748126919" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="https://www.instagram.com/rivv_premium_sneakers01/" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/products" className="text-gray-300 hover:text-white transition-colors text-sm">
                  All Products
                </Link>
              </li>
              <li>
                <Link href="/orders" className="text-gray-300 hover:text-white transition-colors text-sm">
                  My Orders
                </Link>
              </li>
              {/* <li>
                <Link href="/favourites" className="text-gray-300 hover:text-white transition-colors text-sm">
                  Favourites
                </Link>
              </li> */}
              <li>
                <Link href="/profile" className="text-gray-300 hover:text-white transition-colors text-sm">
                  Profile
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Customer Service</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white transition-colors text-sm">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-300">Customer Support</p>
                  <a href="https://wa.me/26662844473" className="text-sm text-white hover:text-primary/80 transition-colors">
                    +266 62844473
                  </a>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-300">Email</p>
                  <a href="mailto:<EMAIL>" className="text-sm text-white hover:text-primary/80 transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-300">Location</p>
                  <p className="text-sm text-white">Maseru, Lesotho</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div>
              <h5 className="text-sm font-semibold mb-2">We Accept</h5>
              <div className="flex items-center gap-4 text-sm text-gray-300">
                <span>M-Pesa</span>
                <span>•</span>
                <span>EcoCash</span>
                <span>•</span>
                <span>Bank Transfer</span>
              </div>
            </div>
            <div className="text-center md:text-right">
              <p className="text-sm text-gray-300">
                Free delivery in Maseru • Free delivery for orders over M3500
              </p>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-sm text-gray-400">
            © {new Date().getFullYear()} RIVV Premium Sneakers. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
