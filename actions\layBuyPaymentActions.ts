/**
 * Lay-Buy Payment Management Actions
 * Server actions for managing payments, verifications, and balance updates
 */

import prisma from "@/lib/prisma";
import { calculateRefundAmount } from "@/lib/lay-buy-utils";
import { sendEmail } from "@/lib/email";
import { formatPrice } from "@/lib/product-utils";
import { sendLayBuyPaymentRejectedEmail } from "@/lib/email-service";
import { sendLayBuyPaymentVerificationEmail } from "@/lib/email-service";

/**
 * Add a new payment to a Lay-Buy order
 */
export async function addLayBuyPayment(paymentData: {
  layBuyOrderId: string;
  amount: number;
  paymentType: "UPFRONT" | "INSTALLMENT" | "COMPLETION";
  paymentMethod?: string;
  paymentProof?: string;
  notes?: string;
  userId?: string; // For customer-initiated payments
}) {
  try {
    // Verify the order exists and get current details
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: paymentData.layBuyOrderId },
      include: {
        user: true,
        payments: true,
      },
    });

    if (!order) {
      return { success: false, error: "Lay-Buy order not found" };
    }

    // Check if order is still active
    if (order.status !== "ACTIVE") {
      return { success: false, error: "Cannot add payments to inactive orders" };
    }

    // Validate payment amount
    const remainingBalance = order.totalAmount - (order?.amountPaid ?? 0);
    if (paymentData.amount <= 0) {
      return { success: false, error: "Payment amount must be greater than zero" };
    }

    if (paymentData.amount > remainingBalance) {
      return { success: false, error: `Payment amount exceeds remaining balance of M${remainingBalance.toFixed(2)}` };
    }

    // Create the payment record
    const payment = await prisma.layBuyPayment.create({
      data: {
        layBuyOrderId: paymentData.layBuyOrderId,
        amount: paymentData.amount,
        paymentType: paymentData.paymentType,
        paymentMethod: paymentData.paymentMethod,
        paymentProof: paymentData.paymentProof,
        notes: paymentData.notes,
        status: "PENDING", // All payments start as pending verification
      },
    });

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error adding Lay-Buy payment:", error);
    return { success: false, error: "Failed to add payment" };
  }
}

/**
 * Verify a payment and update order balance
 */
export async function verifyLayBuyPayment(
  paymentId: string,
  verifiedBy: string,
  status: "VERIFIED" | "REJECTED",
  adminNotes?: string
) {
  try {
    const payment = await prisma.layBuyPayment.findUnique({
      where: { id: paymentId },
      include: {
        layBuyOrder: {
          include: {
            user: true,
            payments: {
              where: { status: "VERIFIED" },
            },
          },
        },
      },
    });

    if (!payment) {
      return { success: false, error: "Payment not found" };
    }

    if (payment.status !== "PENDING") {
      return { success: false, error: "Payment has already been processed" };
    }

    const result = await prisma.$transaction(async (tx) => {
      // Update payment status
      const updatedPayment = await tx.layBuyPayment.update({
        where: { id: paymentId },
        data: {
          status,
          verifiedBy,
          verifiedAt: new Date(),
          notes: adminNotes ? `${payment.notes || ""}\nAdmin: ${adminNotes}`.trim() : payment.notes,
        },
      });

      if (status === "VERIFIED") {
        // Update order balance
        let newAmountPaid = (payment.layBuyOrder.amountPaid ?? 0) + (payment.amount ?? 0);
        if (newAmountPaid > (payment.layBuyOrder.totalAmount ?? 0)) {
          return { payment: updatedPayment, order: payment.layBuyOrder, completed: false, error: "Total payments cannot exceed order total." };
        }
        const isCompleted = newAmountPaid >= (payment.layBuyOrder.totalAmount ?? 0);
        // If order was paused, set back to ACTIVE (unless completed)
        const newStatus = isCompleted ? "COMPLETED" : payment.layBuyOrder.status;
        const updatedOrder = await tx.layBuyOrder.update({
          where: { id: payment.layBuyOrderId },
          data: {
            amountPaid: newAmountPaid,
            status: newStatus,
            completedAt: isCompleted ? new Date() : null,
            updatedAt: new Date(),
          },
        });
        return { payment: updatedPayment, order: updatedOrder, completed: isCompleted };
      }

      if (status === "REJECTED") {
        // Pause the order
        const updatedOrder = await tx.layBuyOrder.update({
          where: { id: payment.layBuyOrderId },
          data: {
            status: "ACTIVE",
            updatedAt: new Date(),
          },
        });
        // Send rejection email to customer
        const user = payment.layBuyOrder.user;
        if (user?.email) {
          await sendLayBuyPaymentRejectedEmail({
            to: user.email,
            customerName: user.name || "Customer",
            orderNumber: payment.layBuyOrder.orderNumber,
            paymentAmount: `M${(payment.amount ?? 0).toFixed(2)}`,
            rejectionReason: adminNotes,
            orderUrl: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/lay-buy-orders/${payment.layBuyOrderId}`
          });
        }
        return { payment: updatedPayment, order: updatedOrder, completed: false };
      }

      return { payment: updatedPayment, order: payment.layBuyOrder, completed: false };
    });

    // Send email notification if payment was verified
    if (status === "VERIFIED" && result.payment) {
      try {
        const emailData = {
          customerName: payment.layBuyOrder.user?.name || "Customer",
          orderNumber: payment.layBuyOrder.orderNumber,
          paymentAmount: formatPrice(payment.amount || 0),
          remainingBalance: formatPrice((payment.layBuyOrder.totalAmount || 0) - (result.order.amountPaid || 0)),
          isCompleted: result.completed,
          orderUrl: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/lay-buy-orders/${payment.layBuyOrderId}`,
        };

        const emailTemplate = {
          subject: result.completed 
            ? `🎉 Lay-Buy Payment Complete - Order ${payment.layBuyOrder.orderNumber} | RIVV`
            : `✅ Payment Verified - Order ${payment.layBuyOrder.orderNumber} | RIVV`,
          html: `
            <!DOCTYPE html>
            <html>
              <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Payment Verified</title>
                <style>
                  body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                  .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                  .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                  .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
                  .payment-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
                  .cta-button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                  .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="header">
                    <h1>${result.completed ? '🎉 Payment Complete!' : '✅ Payment Verified'}</h1>
                    <p>Order ${payment.layBuyOrder.orderNumber}</p>
                  </div>

                  <div class="content">
                    <h2>Hi ${emailData.customerName},</h2>

                    <p>Great news! Your payment of <strong>${emailData.paymentAmount}</strong> has been verified and applied to your Lay-Buy order.</p>

                    <div class="payment-info">
                      <h3>Payment Details</h3>
                      <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                        <span>Payment Amount:</span>
                        <strong style="color: #10b981;">${emailData.paymentAmount}</strong>
                      </div>
                      <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                        <span>Remaining Balance:</span>
                        <strong style="color: ${result.completed ? '#10b981' : '#f59e0b'};">${emailData.remainingBalance}</strong>
                      </div>
                    </div>

                    ${result.completed ? `
                      <div style="background: #d1fae5; padding: 15px; border-radius: 6px; text-align: center; margin: 20px 0;">
                        <h3 style="margin: 0; color: #065f46;">🎉 Congratulations!</h3>
                        <p style="margin: 10px 0 0 0; color: #065f46;">Your Lay-Buy order is now complete! We'll process your order and prepare it for delivery.</p>
                      </div>
                    ` : `
                      <p>You still have <strong>${emailData.remainingBalance}</strong> remaining to complete your Lay-Buy order.</p>
                    `}

                    <div style="text-align: center;">
                      <a href="${emailData.orderUrl}" class="cta-button">View Order Details</a>
                    </div>

                    <p>Thank you for choosing RIVV!</p>
                  </div>

                  <div class="footer">
                    <p>RIVV - Your Fashion Destination</p>
                    <p>This is an automated notification. Please do not reply to this email.</p>
                  </div>
                </div>
              </body>
            </html>
          `,
          text: `
            ${result.completed ? '🎉 Payment Complete!' : '✅ Payment Verified'}

            Hi ${emailData.customerName},

            Great news! Your payment of ${emailData.paymentAmount} has been verified and applied to your Lay-Buy order.

            Payment Details:
            - Payment Amount: ${emailData.paymentAmount}
            - Remaining Balance: ${emailData.remainingBalance}

            ${result.completed ? `
            🎉 Congratulations! Your Lay-Buy order is now complete! We'll process your order and prepare it for delivery.
            ` : `
            You still have ${emailData.remainingBalance} remaining to complete your Lay-Buy order.
            `}

            View order details: ${emailData.orderUrl}

            Thank you for choosing RIVV!
          `,
        };

        await sendLayBuyPaymentVerificationEmail({
          to: payment.layBuyOrder.user?.email || "",
          customerName: payment.layBuyOrder.user?.name || "Customer",
          orderNumber: payment.layBuyOrder.orderNumber,
          paymentAmount: formatPrice(payment.amount || 0),
          remainingBalance: formatPrice((payment.layBuyOrder.totalAmount || 0) - (result.order.amountPaid || 0)),
          isCompleted: result.completed,
          orderUrl: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/lay-buy-orders/${payment.layBuyOrderId}`
        });
      } catch (emailError) {
        console.error("Failed to send payment verification email:", emailError);
        // Don't fail the verification if email fails
      }
    }

    return { success: true, data: result };
  } catch (error) {
    console.error("Error verifying Lay-Buy payment:", error);
    return { success: false, error: "Failed to verify payment" };
  }
}

/**
 * Cancel a Lay-Buy order and process refund
 */
export async function cancelLayBuyOrder(
  orderId: string,
  cancelledBy: string,
  reason?: string,
  adminNotes?: string
) {
  try {
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        orderItems: true,
        payments: {
          where: { status: "VERIFIED" },
        },
      },
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    if (order.status !== "ACTIVE") {
      return { success: false, error: "Can only cancel active orders" };
    }

    // Calculate refund amount based on timing
    const dueDate = order.dueDate ? new Date(order.dueDate) : new Date();
    const refundInfo = calculateRefundAmount(order.amountPaid ?? 0, dueDate);

    const result = await prisma.$transaction(async (tx) => {
      // Update order status
      const updatedOrder = await tx.layBuyOrder.update({
        where: { id: orderId },
        data: {
          status: "CANCELLED",
          cancelledAt: new Date(),
          refundAmount: refundInfo.refundAmount,
          adminNotes: adminNotes ? `${order.adminNotes || ""}\nCancellation: ${adminNotes}`.trim() : order.adminNotes,
          updatedAt: new Date(),
        },
      });

      const stockUpdates = await Promise.all(
        (order?.orderItems ?? [])
          .filter((item: any) => item.productId !== null)
          .map((item: any) =>
            tx.product.update({
              where: { id: item.productId! },
              data: {
                stock: {
                  increment: item.quantity,
                },
              },
            })
          )
      );

      return { order: updatedOrder, refundInfo, stockUpdates };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error cancelling Lay-Buy order:", error);
    return { success: false, error: "Failed to cancel order" };
  }
}

/**
 * Forfeit a Lay-Buy order (no refund)
 */
export async function forfeitLayBuyOrder(
  orderId: string,
  forfeitedBy: string,
  reason?: string
) {
  try {
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      include: {
        orderItems: true,
      },
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    if (order.status !== "ACTIVE") {
      return { success: false, error: "Can only forfeit active orders" };
    }

    const result = await prisma.$transaction(async (tx) => {
      // Update order status
      const updatedOrder = await tx.layBuyOrder.update({
        where: { id: orderId },
        data: {
          status: "FORFEITED",
          forfeitedAt: new Date(),
          refundAmount: 0, // No refund for forfeited orders
          adminNotes: reason ? `${order.adminNotes || ""}\nForfeited: ${reason}`.trim() : order.adminNotes,
          updatedAt: new Date(),
        },
      });

      // Restore product stock
      const stockUpdates = await Promise.all(
        (order.orderItems ?? [])
          .filter((item) => item.productId !== null)
          .map((item) =>
            tx.product.update({
              where: { id: item.productId! },
              data: {
                stock: {
                  increment: item.quantity,
                },
              },
            })
          )
      );

      return { order: updatedOrder, stockUpdates };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error forfeiting Lay-Buy order:", error);
    return { success: false, error: "Failed to forfeit order" };
  }
}

/**
 * Get payment history for an order
 */
export async function getLayBuyPaymentHistory(orderId: string) {
  try {
    const payments = await prisma.layBuyPayment.findMany({
      where: { layBuyOrderId: orderId },
      orderBy: { createdAt: "desc" },
    });

    return { success: true, data: payments };
  } catch (error) {
    console.error("Error fetching payment history:", error);
    return { success: false, error: "Failed to fetch payment history" };
  }
}

/**
 * Get pending payments for admin review
 */
export async function getPendingPayments(filters?: {
  page?: number;
  limit?: number;
}) {
  try {
    const { page = 1, limit = 20 } = filters || {};
    const skip = (page - 1) * limit;

    const [payments, total] = await Promise.all([
      prisma.layBuyPayment.findMany({
        where: { status: "PENDING" },
        include: {
          layBuyOrder: {
            include: {
              user: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.layBuyPayment.count({
        where: { status: "PENDING" },
      }),
    ]);

    return {
      success: true,
      data: {
        payments,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  } catch (error) {
    console.error("Error fetching pending payments:", error);
    return { success: false, error: "Failed to fetch pending payments" };
  }
}
