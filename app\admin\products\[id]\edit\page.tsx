"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import ProductForm from "@/components/admin/products/product-form";
import ProductImageReplacement from "@/components/admin/products/product-image-replacement";
import QuickCategoryChanger from "@/components/admin/products/quick-category-changer";
import { getCategories } from "@/actions/categoryActions";
import { authClient } from "@/lib/auth-client";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { UserRole } from "@/utils/types";
import { LESOTHO_DISTRICTS, calculateDeliveryFee } from "@/lib/product-utils";
import { toast } from "react-hot-toast";

type Category = {
  id: string;
  name: string;
  description?: string;
};

export default function AdminEditProductPage() {
  const { id } = useParams();
  const router = useRouter();
  const [product, setProduct] = useState<any>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const { data, isPending } = authClient.useSession();

  useEffect(() => {
    if (!isPending && !data?.user) {
      router.replace("/sign-in");
    }
  }, [isPending, data, router]);

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true);
      setError("");
      try {
        const res = await fetch(`/api/products/${id}`);
        const data = await res.json();
        if (data.success) {
          setProduct(data.data);
        } else {
          setError(data.error || "Product not found");
        }
      } catch (err) {
        setError("Failed to fetch product");
      } finally {
        setLoading(false);
      }
    };
    if (id) fetchProduct();
  }, [id]);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const result = await getCategories();
        if (result.success && result.data) {
          setCategories(
            result.data.map((cat: any) => ({
              id: cat.id,
              name: cat.name,
              description: cat.description ?? undefined,
            }))
          );
        }
      } catch (error) {
        console.error("Error loading categories:", error);
      } finally {
        setLoadingCategories(false);
      }
    };
    loadCategories();
  }, []);

  const handleUpdate = async (data: any) => {
    setSubmitLoading(true);
    setError("");
    try {
      const res = await fetch(`/api/admin/products/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      const result = await res.json();
      if (result.success) {
        toast.success("Product updated!");
        router.replace("/admin/products");
      } else {
        setError(result.error || "Failed to update product");
      }
    } catch (err) {
      setError("Failed to update product");
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleImagesUpdated = (newImages: string[]) => {
    if (product) {
      setProduct({
        ...product,
        images: newImages,
      } as any);
    }
  };

  const handleCategoryChanged = (newCategory: any) => {
    if (product) {
      setProduct({
        ...product,
        category: newCategory,
        categoryId: newCategory.id,
      } as any);
    }
  };

  if (isPending) {
    return (
      <div className="flex justify-center items-center h-screen w-full">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (!data?.user) {
    // Optionally, you can return null or a loading state here,
    // but the redirect will handle navigation.
    return (
      <div className="flex w-full h-full justify-center items-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout
      user={{
        id: data.user.id,
        name: data.user.name,
        email: data.user.email,
        emailVerified: data.user.emailVerified,
        createdAt: data.user.createdAt,
        updatedAt: data.user.updatedAt,
        image: data.user.image,
        role: (data.user as any)["role"] ?? UserRole.ADMIN,
      }}
    >
      <div className="max-w-4xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-4">Edit Product</h1>
        {loading || loadingCategories ? (
          <div className="flex justify-center items-center h-40">
            <SpinnerCircle4 />
          </div>
        ) : error ? (
          <div className="text-red-600">{error}</div>
        ) : product ? (
          <div className="space-y-6">
            {/* Quick Category Changer */}
            <QuickCategoryChanger
              product={product as any}
              categories={categories}
              onCategoryChanged={handleCategoryChanged}
            />

            {/* Image Replacement Section */}
            <ProductImageReplacement
              productId={(product as any).id}
              images={(product as any).images}
              onImagesUpdated={handleImagesUpdated}
            />

            {/* Product Form */}
            <ProductForm
              initialProduct={product}
              categories={categories}
              onSubmit={handleUpdate}
              loading={submitLoading}
              error={error}
              mode="edit"
            />
          </div>
        ) : null}
      </div>
    </AdminLayout>
  );
}
