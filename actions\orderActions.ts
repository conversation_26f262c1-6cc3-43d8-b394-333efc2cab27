"use server";

import prisma from "@/lib/prisma";
import { OrderStatus } from "@/utils/types";
import { sendOrderStatusUpdateEmail } from "@/lib/email-service";

/**
 * Get all orders with optional filtering
 */
export async function getOrders(filters?: {
  search?: string;
  status?: OrderStatus;
  userId?: string;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    const where: any = {};

    if (filters?.search) {
      where.OR = [
        { orderNumber: { contains: filters.search, mode: "insensitive" } },
        { user: { name: { contains: filters.search, mode: "insensitive" } } },
        { user: { email: { contains: filters.search, mode: "insensitive" } } }
      ];
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.userId) {
      where.userId = filters.userId;
    }

    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.createdAt.lte = filters.endDate;
      }
    }

    const orders = await prisma.order.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                images: true
              }
            }
          }
        },
        _count: {
          select: {
            orderItems: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });

    return { success: true, data: orders };
  } catch (error) {
    console.error("Error fetching orders:", error);
    return { success: false, error: "Failed to fetch orders" };
  }
}

/**
 * Get a single order by ID
 */
export async function getOrderById(orderId: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        orderItems: {
          select: {
            id: true,
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                images: true,
                price: true
              }
            },
            quantity: true,
            price: true,
            size: true,
            color: true,
            costPrice: true,
            shippingFee: true,
            lateCollectionFee: true,
            totalCost: true,
            profit: true,
          }
        },
        paymentProof: true,
        discountCode: true
      }
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    return { success: true, data: order };
  } catch (error) {
    console.error("Error fetching order:", error);
    return { success: false, error: "Failed to fetch order" };
  }
}

/**
 * Create a new order
 */
export async function createOrder(orderData: {
  userId: string;
  orderNumber: string;
  totalAmount: number;
  discountAmount?: number;
  shippingAddress: string;
  phoneNumber: string;
  notes?: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    size: string;
    color: string;
  }>;
}) {
  try {
    const order = await prisma.order.create({
      data: {
        userId: orderData.userId,
        orderNumber: orderData.orderNumber,
        status: "PENDING",
        totalAmount: orderData.totalAmount,
        discountAmount: orderData.discountAmount || 0,
        shippingAddress: orderData.shippingAddress,
        phoneNumber: orderData.phoneNumber,
        notes: orderData.notes,
        orderItems: {
          create: orderData.items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            size: item.size,
            color: item.color
          }))
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                images: true
              }
            }
          }
        }
      }
    });

    // Update product stock for each item
    for (const item of orderData.items) {
      await prisma.product.update({
        where: { id: item.productId },
        data: {
          stock: {
            decrement: item.quantity
          }
        }
      });
    }

    return { success: true, data: order };
  } catch (error) {
    console.error("Error creating order:", error);
    return { success: false, error: "Failed to create order" };
  }
}

/**
 * Update order status and admin notes
 */
export async function updateOrder(orderId: string, updateData: {
  status?: OrderStatus;
  adminNotes?: string;
  trackingNumber?: string;
}) {
  try {
    const order = await prisma.order.update({
      where: { id: orderId },
      data: {
        ...updateData,
        updatedAt: new Date()
      },
      include: {
        user: true,
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                images: true
              }
            }
          }
        }
      }
    });

    // Send order status update email to customer
    if (order.user?.email) {
      try {
        // Create a properly typed order object for the email function
        const orderForEmail = {
          id: order.id,
          orderNumber: order.orderNumber,
          totalAmount: order.totalAmount,
          user: order.user,
          orderItems: order.orderItems
            .filter(item => item.product !== null)
            .map(item => ({
              quantity: item.quantity,
              price: item.price,
              size: item.size,
              product: {
                name: item.product!.name,
              },
            })),
        };
        await sendOrderStatusUpdateEmail(orderForEmail, updateData.status || order.status);
      } catch (emailError) {
        console.error("Failed to send status update email:", emailError);
        // Don't fail the request if email fails
      }
    }

    return { success: true, data: order };
  } catch (error) {
    console.error("Error updating order:", error);
    return { success: false, error: "Failed to update order" };
  }
}

/**
 * Get orders by user ID
 */
export async function getOrdersByUserId(userId: string) {
  try {
    const orders = await prisma.order.findMany({
      where: { userId },
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                images: true
              }
            }
          }
        },
        _count: {
          select: {
            orderItems: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });

    return { success: true, data: orders };
  } catch (error) {
    console.error("Error fetching user orders:", error);
    return { success: false, error: "Failed to fetch user orders" };
  }
}

/**
 * Cancel an order
 */
export async function cancelOrder(orderId: string, reason?: string) {
  try {
    // Get order details first
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        orderItems: true
      }
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    if (order.status === "DELIVERED" || order.status === "CANCELLED") {
      return { success: false, error: "Cannot cancel this order" };
    }

    // Update order status
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        status: "CANCELLED",
        adminNotes: reason || "Order cancelled",
        updatedAt: new Date()
      }
    });

    // Restore product stock
    for (const item of order.orderItems) {
      if (item.productId !== null) {
        await prisma.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              increment: item.quantity
            }
          }
        });
      }
    }

    return { success: true, data: updatedOrder };
  } catch (error) {
    console.error("Error cancelling order:", error);
    return { success: false, error: "Failed to cancel order" };
  }
}

/**
 * Get order statistics
 */
export async function getOrderStats() {
  try {
    const [
      totalOrders,
      pendingOrders,
      confirmedOrders,
      shippedOrders,
      deliveredOrders,
      cancelledOrders,
      totalRevenue,
      todayOrders
    ] = await Promise.all([
      prisma.order.count(),
      prisma.order.count({ where: { status: "PENDING" } }),
      prisma.order.count({ where: { status: "CONFIRMED" } }),
      prisma.order.count({ where: { status: "SHIPPED" } }),
      prisma.order.count({ where: { status: "DELIVERED" } }),
      prisma.order.count({ where: { status: "CANCELLED" } }),
      prisma.order.aggregate({
        _sum: { totalAmount: true },
        where: { status: { not: "CANCELLED" } }
      }),
      prisma.order.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      })
    ]);

    return {
      success: true,
      data: {
        totalOrders,
        pendingOrders,
        confirmedOrders,
        shippedOrders,
        deliveredOrders,
        cancelledOrders,
        totalRevenue: totalRevenue._sum.totalAmount || 0,
        todayOrders
      }
    };
  } catch (error) {
    console.error("Error fetching order stats:", error);
    return { success: false, error: "Failed to fetch order statistics" };
  }
}
