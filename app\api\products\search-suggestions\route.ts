import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// GET /api/products/search-suggestions - Get product suggestions for search
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    // Search products by name, brand, or description
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        OR: [
          {
            name: {
              contains: query,
              mode: "insensitive"
            }
          },
          {
            brand: {
              contains: query,
              mode: "insensitive"
            }
          },
          {
            description: {
              contains: query,
              mode: "insensitive"
            }
          }
        ]
      },
      select: {
        id: true,
        name: true,
        brand: true,
        price: true,
        discountedPrice: true,
        images: true,
        stock: true
      },
      orderBy: [
        { stock: "desc" }, // Prioritize in-stock items
        { name: "asc" }
      ],
      take: 8
    });

    const response: ApiResponse<typeof products> = {
      success: true,
      data: products
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching search suggestions:", error);
    
    return NextResponse.json(
      { success: false, error: "Failed to fetch suggestions" },
      { status: 500 }
    );
  }
}
