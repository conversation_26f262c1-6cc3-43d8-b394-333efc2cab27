import { NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// POST /api/admin/products/bulk-category-update - Update categories for multiple products
export async function POST(request: NextRequest) {
  try {
    await requireAdmin();

    const body = await request.json();
    const { updates } = body;

    // Validate input
    if (!Array.isArray(updates) || updates.length === 0) {
      return NextResponse.json(
        { success: false, error: "Updates array is required" },
        { status: 400 }
      );
    }

    // Validate each update
    for (const update of updates) {
      if (!update.productId || !update.newCategoryId) {
        return NextResponse.json(
          { success: false, error: "Each update must have productId and newCategoryId" },
          { status: 400 }
        );
      }
    }

    // Verify all categories exist
    const categoryIds = [...new Set(updates.map(u => u.newCategoryId))];
    const categories = await prisma.category.findMany({
      where: { id: { in: categoryIds } },
    });

    if (categories.length !== categoryIds.length) {
      return NextResponse.json(
        { success: false, error: "One or more categories not found" },
        { status: 400 }
      );
    }

    // Verify all products exist
    const productIds = updates.map(u => u.productId);
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
      include: { category: true },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { success: false, error: "One or more products not found" },
        { status: 400 }
      );
    }

    // Perform bulk update
    const updatePromises = updates.map(update => 
      prisma.product.update({
        where: { id: update.productId },
        data: { categoryId: update.newCategoryId },
        include: { category: true },
      })
    );

    const updatedProducts = await Promise.all(updatePromises);

    // Log the changes for audit purposes
    updates.forEach((update, index) => {
      const product = products.find(p => p.id === update.productId);
      const newCategory = categories.find(c => c.id === update.newCategoryId);

      console.log(`Category changed for product ${product?.name}: ${product?.category.name} -> ${newCategory?.name}`);
    });

    const response: ApiResponse<typeof updatedProducts> = {
      success: true,
      data: updatedProducts,
      message: `Successfully updated ${updatedProducts.length} products`,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating product categories:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update product categories" },
      { status: 500 }
    );
  }
}
