"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UploadDropzone } from "@/lib/uploadthing";
import { MessageSquare, Upload, Mail, CheckCircle, AlertCircle, Search, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSession } from "@/lib/auth-client";
import Link from "next/link";
import NavBar from "@/components/navbar";
import NavBarSkeleton from "@/components/navBarSkeleton";
import Footer from "@/components/footer";
import { User } from "@/utils/types";

export default function CustomRequestPage() {
  const { data: session, isPending } = useSession();
  const [form, setForm] = useState({
    name: "",
    email: "",
    shoeName: "",
    brand: "",
    color: "",
    size: "",
    shoeType: "",
    description: "",
    imageUrl: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle");
  const [error, setError] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm({ ...form, [name]: value });
  };

  const handleImageUpload = (uploadedFiles: any[]) => {
    if (uploadedFiles && uploadedFiles.length > 0) {
      setForm({ ...form, imageUrl: uploadedFiles[0].url });
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch("/api/custom-request", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(form),
      });

      const data = await response.json();

      if (data.success) {
        setSubmitStatus("success");
        setForm({
          name: "",
          email: "",
          shoeName: "",
          brand: "",
          color: "",
          size: "",
          shoeType: "",
          description: "",
          imageUrl: "",
        });
      } else {
        setError(data.error || "Failed to submit request");
        setSubmitStatus("error");
      }
    } catch (err) {
      setError("Network error. Please try again.");
      setSubmitStatus("error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {isPending || !session?.user ? (
        <NavBarSkeleton loading={isPending} user={null} />
      ) : (
        <NavBar user={session.user as User} loading={false} />
      )}
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          <div className="text-center">
            <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-blue-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Custom Shoe Request
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Can't find that perfect pair? Upload a picture and tell us what you're looking for. 
              Our team will search our supplier network to find it for you!
            </p>
          </div>
        </div>

        {submitStatus === "success" && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span>
                  Request submitted successfully! You'll receive a confirmation email shortly, and we'll get back to you soon.
                </span>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {submitStatus === "error" && error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Tell Us What You're Looking For
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Your Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={form.name}
                    onChange={handleChange}
                    required
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={form.email}
                    onChange={handleChange}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Shoe Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Shoe Details</h3>
                
                <div>
                  <Label htmlFor="shoeName">Shoe Name/Model *</Label>
                  <Input
                    id="shoeName"
                    name="shoeName"
                    value={form.shoeName}
                    onChange={handleChange}
                    required
                    placeholder="e.g., Air Jordan 1 Retro High OG, Nike Dunk Low, etc."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="brand">Brand</Label>
                    <Select onValueChange={(value) => handleSelectChange("brand", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select brand" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Nike">Nike</SelectItem>
                        <SelectItem value="Adidas">Adidas</SelectItem>
                        <SelectItem value="Jordan">Jordan</SelectItem>
                        <SelectItem value="Puma">Puma</SelectItem>
                        <SelectItem value="Vans">Vans</SelectItem>
                        <SelectItem value="Converse">Converse</SelectItem>
                        <SelectItem value="New Balance">New Balance</SelectItem>
                        <SelectItem value="Reebok">Reebok</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="size">Size</Label>
                    <Input
                      id="size"
                      name="size"
                      value={form.size}
                      onChange={handleChange}
                      placeholder="e.g., 9, 10.5, 42"
                    />
                  </div>

                  <div>
                    <Label htmlFor="shoeType">Type</Label>
                    <Select onValueChange={(value) => handleSelectChange("shoeType", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Sneakers">Sneakers</SelectItem>
                        <SelectItem value="Basketball">Basketball</SelectItem>
                        <SelectItem value="Running">Running</SelectItem>
                        <SelectItem value="Lifestyle">Lifestyle</SelectItem>
                        <SelectItem value="Skateboarding">Skateboarding</SelectItem>
                        <SelectItem value="Boots">Boots</SelectItem>
                        <SelectItem value="Sandals">Sandals</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="color">Color/Colorway</Label>
                  <Input
                    id="color"
                    name="color"
                    value={form.color}
                    onChange={handleChange}
                    placeholder="e.g., Black/White, Bred, Chicago, etc."
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description">Additional Details *</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  required
                  rows={4}
                  placeholder="Please provide any additional details about the shoe you're looking for. Include specific features, release year, or any other information that might help us find it."
                />
              </div>

              {/* Image Upload */}
              <div>
                <Label>Upload Image (Optional)</Label>
                <p className="text-sm text-gray-600 mb-2">
                  Upload a picture of the shoe you're looking for to help us identify it better.
                </p>
                
                {form.imageUrl ? (
                  <div className="space-y-2">
                    <div className="relative w-32 h-32 border rounded-lg overflow-hidden">
                      <img
                        src={form.imageUrl}
                        alt="Uploaded shoe"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setForm({ ...form, imageUrl: "" })}
                    >
                      Remove Image
                    </Button>
                  </div>
                ) : (
                  <UploadDropzone
                    endpoint="productImageUploader"
                    onClientUploadComplete={handleImageUpload}
                    onUploadError={(error: Error) => {
                      setError(`Upload failed: ${error.message}`);
                    }}
                    onUploadBegin={() => setIsUploading(true)}
                    className="border-2 border-dashed border-gray-300 rounded-lg p-6"
                  />
                )}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting || isUploading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting Request...
                    </>
                  ) : (
                    <>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Submit Request
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Information Section */}
        <Card className="mt-8">
          <CardContent className="pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">What Happens Next?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-sm">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Request Review</h4>
                    <p className="text-sm text-gray-600">Our team will review your request within 24 hours</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-sm">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Supplier Search</h4>
                    <p className="text-sm text-gray-600">We'll search our network of trusted suppliers worldwide</p>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-sm">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Email Update</h4>
                    <p className="text-sm text-gray-600">You'll receive an email with availability and pricing</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 font-semibold text-sm">4</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Order & Delivery</h4>
                    <p className="text-sm text-gray-600">If available, we'll provide ordering and delivery details</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  );
}
