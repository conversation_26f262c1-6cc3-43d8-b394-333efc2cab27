import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/combo-deals - Get active combo deals for public display
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get("featured") === "true";
    const limit = parseInt(searchParams.get("limit") || "10");

    // Build where clause for active and non-expired deals
    const where: any = {
      isActive: true,
      validUntil: {
        gt: new Date(),
      },
    };

    if (featured) {
      where.isFeatured = true;
    }

    const combos = await prisma.comboDeal.findMany({
      where,
      take: limit,
      orderBy: [
        { isFeatured: "desc" },
        { createdAt: "desc" },
      ],
      include: {
        comboProducts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                brand: true,
                price: true,
                discountedPrice: true,
                images: true,
                stock: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    // Filter out combos with inactive or out-of-stock products
    const availableCombos = combos.filter(combo => 
      combo.comboProducts.every(cp => 
        cp.product.isActive && cp.product.stock > 0
      )
    );

    return NextResponse.json({
      success: true,
      data: availableCombos,
    });
  } catch (error) {
    console.error("Error fetching combo deals:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch combo deals" },
      { status: 500 }
    );
  }
}
