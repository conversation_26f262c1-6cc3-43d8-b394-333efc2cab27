/**
 * Search Analytics System
 * Tracks search queries, results, and user behavior for optimization
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface SearchAnalytics {
  query: string;
  resultsCount: number;
  clickedProductId?: string;
  userAgent?: string;
  timestamp: Date;
  searchDuration?: number;
  filters?: {
    category?: string;
    brand?: string;
    priceRange?: { min?: number; max?: number };
    colors?: string[];
    sizes?: string[];
  };
}

export interface PopularSearch {
  query: string;
  count: number;
  avgResults: number;
  clickThroughRate: number;
}

export interface SearchInsights {
  totalSearches: number;
  uniqueQueries: number;
  avgResultsPerSearch: number;
  topQueries: PopularSearch[];
  failedSearches: { query: string; count: number }[];
  popularFilters: {
    brands: { name: string; count: number }[];
    categories: { name: string; count: number }[];
    priceRanges: { range: string; count: number }[];
  };
  searchTrends: {
    hourly: { hour: number; count: number }[];
    daily: { date: string; count: number }[];
  };
}

/**
 * Log a search query and its results
 */
export async function logSearch(analytics: SearchAnalytics): Promise<void> {
  try {
    // For now, we'll store this in a simple way
    // In a production system, you might want a dedicated analytics table
    console.log('Search Analytics:', {
      query: analytics.query,
      resultsCount: analytics.resultsCount,
      timestamp: analytics.timestamp,
      filters: analytics.filters
    });
    
    // You could store this in a database table or send to an analytics service
    // await prisma.searchAnalytics.create({ data: analytics });
  } catch (error) {
    console.error('Error logging search analytics:', error);
  }
}

/**
 * Get search insights for admin dashboard
 */
export async function getSearchInsights(
  startDate?: Date,
  endDate?: Date
): Promise<SearchInsights> {
  try {
    // For now, return mock data based on your product data
    // In production, this would query actual search analytics
    
    const products = await prisma.product.findMany({
      include: { category: true },
      where: { isActive: true }
    });
    
    // Generate insights based on product data and common search patterns
    const brands = products.reduce((acc, product) => {
      acc[product.brand] = (acc[product.brand] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const categories = products.reduce((acc, product) => {
      acc[product.category.name] = (acc[product.category.name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Mock popular searches based on your product data
    const topQueries: PopularSearch[] = [
      { query: 'nike air jordan', count: 156, avgResults: 12, clickThroughRate: 0.78 },
      { query: 'adidas ultraboost', count: 134, avgResults: 8, clickThroughRate: 0.82 },
      { query: 'vans old skool', count: 98, avgResults: 6, clickThroughRate: 0.75 },
      { query: 'puma suede', count: 87, avgResults: 5, clickThroughRate: 0.71 },
      { query: 'lacoste sneakers', count: 76, avgResults: 9, clickThroughRate: 0.69 },
      { query: 'basketball shoes', count: 65, avgResults: 15, clickThroughRate: 0.73 },
      { query: 'running shoes', count: 58, avgResults: 18, clickThroughRate: 0.76 },
      { query: 'casual sneakers', count: 52, avgResults: 22, clickThroughRate: 0.68 }
    ];
    
    const failedSearches = [
      { query: 'yeezy boost', count: 23 },
      { query: 'jordan 11', count: 18 },
      { query: 'air max 90', count: 15 },
      { query: 'stan smith', count: 12 }
    ];
    
    // Generate hourly trends (mock data)
    const hourlyTrends = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: Math.floor(Math.random() * 50) + 10
    }));
    
    // Generate daily trends for last 7 days
    const dailyTrends = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return {
        date: date.toISOString().split('T')[0],
        count: Math.floor(Math.random() * 200) + 50
      };
    }).reverse();
    
    return {
      totalSearches: 1247,
      uniqueQueries: 342,
      avgResultsPerSearch: 8.5,
      topQueries,
      failedSearches,
      popularFilters: {
        brands: Object.entries(brands)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 10),
        categories: Object.entries(categories)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count),
        priceRanges: [
          { range: 'M500-M1000', count: 234 },
          { range: 'M1000-M1500', count: 189 },
          { range: 'M1500-M2000', count: 156 },
          { range: 'Under M500', count: 98 },
          { range: 'Over M2000', count: 67 }
        ]
      },
      searchTrends: {
        hourly: hourlyTrends,
        daily: dailyTrends
      }
    };
  } catch (error) {
    console.error('Error getting search insights:', error);
    throw error;
  }
}

/**
 * Get search suggestions based on popular queries and product data
 */
export async function getSearchSuggestions(query: string, limit: number = 8): Promise<string[]> {
  try {
    const queryLower = query.toLowerCase();
    const suggestions = new Set<string>();
    
    // Get suggestions from product names, brands, and tags
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { brand: { contains: query, mode: 'insensitive' } },
          { tags: { hasSome: [queryLower] } },
          { keywords: { hasSome: [queryLower] } }
        ]
      },
      select: {
        name: true,
        brand: true,
        tags: true,
        keywords: true
      },
      take: 20
    });
    
    // Add brand suggestions
    products.forEach(product => {
      if (product.brand.toLowerCase().includes(queryLower)) {
        suggestions.add(product.brand);
      }
      
      // Add relevant tags
      product.tags?.forEach(tag => {
        if (tag.toLowerCase().includes(queryLower)) {
          suggestions.add(tag);
        }
      });
      
      // Add relevant keywords
      product.keywords?.forEach(keyword => {
        if (keyword.toLowerCase().includes(queryLower) && keyword.length > 2) {
          suggestions.add(keyword);
        }
      });
    });
    
    // Add popular search terms that match
    const popularTerms = [
      'nike air jordan', 'adidas ultraboost', 'vans old skool', 'puma suede',
      'basketball shoes', 'running shoes', 'casual sneakers', 'high top',
      'low top', 'retro sneakers', 'sports shoes', 'lifestyle shoes'
    ];
    
    popularTerms.forEach(term => {
      if (term.toLowerCase().includes(queryLower)) {
        suggestions.add(term);
      }
    });
    
    return Array.from(suggestions).slice(0, limit);
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    return [];
  }
}

/**
 * Optimize search results based on analytics
 */
export async function optimizeSearchResults(query: string, results: any[]): Promise<any[]> {
  try {
    // Apply optimization based on search analytics
    // This could include boosting popular products, adjusting relevance scores, etc.
    
    return results.map(product => ({
      ...product,
      // Boost score for popular brands
      relevanceScore: product.relevanceScore + getBrandBoost(product.brand)
    })).sort((a, b) => b.relevanceScore - a.relevanceScore);
  } catch (error) {
    console.error('Error optimizing search results:', error);
    return results;
  }
}

function getBrandBoost(brand: string): number {
  const brandBoosts: Record<string, number> = {
    'Nike': 10,
    'Adidas': 8,
    'Lacoste': 6,
    'Vans': 5,
    'Puma': 4
  };
  
  return brandBoosts[brand] || 0;
}
