"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UploadDropzone } from "@/lib/uploadthing";
import { MessageSquare, Upload, Mail, CheckCircle, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSession } from "@/lib/auth-client";

interface CustomRequestFormProps {
  className?: string;
}

interface FormData {
  name: string;
  email: string;
  shoeName: string;
  brand: string;
  color: string;
  size: string;
  shoeType: string;
  description: string;
  imageUrl: string;
}

const SHOE_SIZES = ["3", "4", "5", "6", "7", "8", "9", "10", "11", "12"];
const SHOE_TYPES = ["Sneakers", "Boots", "Sandals", "Formal Shoes", "Sports Shoes", "Casual Shoes", "Other"];

export default function CustomRequestForm({ className }: CustomRequestFormProps) {
  const { data: session } = useSession();
  const [formData, setFormData] = useState<FormData>({
    name: session?.user?.name || "",
    email: session?.user?.email || "",
    shoeName: "",
    brand: "",
    color: "",
    size: "",
    shoeType: "",
    description: "",
    imageUrl: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (uploadedFiles: any[]) => {
    if (uploadedFiles && uploadedFiles.length > 0) {
      setFormData(prev => ({ ...prev, imageUrl: uploadedFiles[0].url }));
      setIsUploading(false);
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) return "Name is required";
    if (!formData.email.trim()) return "Email is required";
    if (!/\S+@\S+\.\S+/.test(formData.email)) return "Please enter a valid email address";
    if (!formData.shoeName.trim()) return "Shoe name is required";
    if (!formData.description.trim()) return "Description is required";
    if (formData.description.length < 20) return "Description must be at least 20 characters";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setErrorMessage(validationError);
      setSubmitStatus("error");
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus("idle");
    setErrorMessage("");

    try {
      const response = await fetch("/api/custom-request", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSubmitStatus("success");
        // Reset form
        setFormData({
          name: session?.user?.name || "",
          email: session?.user?.email || "",
          shoeName: "",
          brand: "",
          color: "",
          size: "",
          shoeType: "",
          description: "",
          imageUrl: "",
        });
      } else {
        setSubmitStatus("error");
        setErrorMessage(result.error || "Failed to submit request");
      }
    } catch (error) {
      setSubmitStatus("error");
      setErrorMessage("Failed to submit request. Please try again.");
      console.error("Error submitting custom request:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Can't Find What You're Looking For?
        </CardTitle>
        <p className="text-sm text-gray-600">
          Request a custom shoe that's not in our catalog. Upload a picture and tell us what you're looking for!
        </p>
      </CardHeader>
      <CardContent>
        {submitStatus === "success" && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span>
                  Request submitted successfully! You'll receive a confirmation email shortly, and we'll get back to you soon.
                </span>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {submitStatus === "error" && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {errorMessage}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Your Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter your name"
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Your Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="Enter your email"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="shoeName">Shoe Name *</Label>
              <Input
                id="shoeName"
                value={formData.shoeName}
                onChange={(e) => handleInputChange("shoeName", e.target.value)}
                placeholder="e.g., Air Jordan 1 Retro High"
                required
              />
            </div>
            <div>
              <Label htmlFor="brand">Brand</Label>
              <Input
                id="brand"
                value={formData.brand}
                onChange={(e) => handleInputChange("brand", e.target.value)}
                placeholder="e.g., Nike, Adidas, Puma"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="color">Color</Label>
              <Input
                id="color"
                value={formData.color}
                onChange={(e) => handleInputChange("color", e.target.value)}
                placeholder="e.g., Black/White"
              />
            </div>
            <div>
              <Label htmlFor="size">Size</Label>
              <Select value={formData.size} onValueChange={(value) => handleInputChange("size", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {SHOE_SIZES.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="shoeType">Type</Label>
              <Select value={formData.shoeType} onValueChange={(value) => handleInputChange("shoeType", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {SHOE_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Describe the shoe you're looking for in detail. Include any specific features, style preferences, or additional information that would help us find the perfect match for you."
              rows={4}
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              {formData.description.length}/500 characters (minimum 20)
            </p>
          </div>

          <div>
            <Label>Upload Image (Optional)</Label>
            <p className="text-sm text-gray-600 mb-2">
              Upload a picture of the shoe you're looking for to help us understand your request better.
            </p>
            
            {formData.imageUrl ? (
              <div className="space-y-2">
                <div className="relative inline-block">
                  <img
                    src={formData.imageUrl}
                    alt="Uploaded shoe"
                    className="w-32 h-32 object-cover rounded-lg border"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2"
                    onClick={() => handleInputChange("imageUrl", "")}
                  >
                    ×
                  </Button>
                </div>
                <p className="text-sm text-green-600">Image uploaded successfully!</p>
              </div>
            ) : (
              <div className="space-y-2">
                <UploadDropzone
                  endpoint="productImageUploader"
                  onClientUploadComplete={handleImageUpload}
                  onUploadError={(error: Error) => {
                    setErrorMessage(`Upload failed: ${error.message}`);
                    setSubmitStatus("error");
                    setIsUploading(false);
                  }}
                  onUploadBegin={() => setIsUploading(true)}
                  className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                    isUploading 
                      ? 'border-blue-400 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  appearance={{
                    label: "text-sm",
                    allowedContent: "text-xs",
                  }}
                />
                {isUploading && (
                  <div className="flex items-center justify-center gap-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">Uploading image...</span>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">What happens next?</p>
                <ul className="mt-1 space-y-1 text-blue-700">
                  <li>• You'll receive a confirmation email immediately</li>
                  <li>• Our team will review your request within 24 hours</li>
                  <li>• We'll email you with availability and pricing information</li>
                  <li>• If we can source the shoe, we'll provide ordering details</li>
                </ul>
              </div>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting || isUploading}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting Request...
              </>
            ) : (
              <>
                <MessageSquare className="h-4 w-4 mr-2" />
                Submit Custom Request
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
