"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Package } from "lucide-react";
import { toast } from "react-hot-toast";

interface Category {
  id: string;
  name: string;
  description?: string;
}

interface Product {
  id: string;
  name: string;
  categoryId: string;
  category: {
    id: string;
    name: string;
  };
}

interface QuickCategoryChangerProps {
  product: Product;
  categories: Category[];
  onCategoryChanged?: (newCategory: Category) => void;
}

export default function QuickCategoryChanger({
  product,
  categories,
  onCategoryChanged
}: QuickCategoryChangerProps) {
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [updating, setUpdating] = useState(false);

  const handleCategoryChange = async () => {
    if (!selectedCategoryId) {
      toast.error("Please select a category");
      return;
    }

    if (selectedCategoryId === product.categoryId) {
      toast.error("Product is already in this category");
      return;
    }

    try {
      setUpdating(true);
      
      const response = await fetch(`/api/admin/products/${product.id}/move-category`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          categoryId: selectedCategoryId,
          reason: 'Quick category change from product edit page'
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        const newCategory = categories.find(c => c.id === selectedCategoryId);
        toast.success(`Product moved to ${newCategory?.name} successfully`);
        
        if (onCategoryChanged && newCategory) {
          onCategoryChanged(newCategory);
        }
        
        setSelectedCategoryId("");
      } else {
        toast.error(result.error || 'Failed to update category');
      }
    } catch (err) {
      toast.error('Failed to update category');
      console.error('Error updating category:', err);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Quick Category Change
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2 text-sm">
          <span className="text-gray-600">Current:</span>
          <Badge variant="outline">{product.category.name}</Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Select
            value={selectedCategoryId}
            onValueChange={setSelectedCategoryId}
          >
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select new category..." />
            </SelectTrigger>
            <SelectContent>
              {categories
                .filter(cat => cat.id !== product.categoryId)
                .map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          
          <Button 
            onClick={handleCategoryChange}
            disabled={!selectedCategoryId || updating}
            size="sm"
          >
            {updating ? "Moving..." : "Move"}
            <ArrowRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
        
        {selectedCategoryId && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>Will move to:</span>
            <Badge className="bg-green-100 text-green-800">
              {categories.find(c => c.id === selectedCategoryId)?.name}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
