"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

interface ReferralDiscount {
  code: string;
  type: 'FIXED_AMOUNT' | 'PERCENTAGE';
  value: number;
  partnerName?: string;
  isActive: boolean;
}

interface ReferralContextType {
  referralDiscount: ReferralDiscount | null;
  setReferralDiscount: (discount: ReferralDiscount | null) => void;
  applyReferralToPrice: (originalPrice: number) => number;
  getReferralSavings: (originalPrice: number) => number;
  isReferralActive: boolean;
  clearReferral: () => void;
}

const ReferralContext = createContext<ReferralContextType | undefined>(undefined);

export function ReferralProvider({ children }: { children: React.ReactNode }) {
  const [referralDiscount, setReferralDiscount] = useState<ReferralDiscount | null>(null);

  // Load referral discount from cookies on mount
  useEffect(() => {
    const loadReferralFromCookies = async () => {
      if (typeof window === "undefined") return;

      // Check for referral code in URL first
      const url = new URL(window.location.href);
      const refCode = url.searchParams.get("ref");
      const discountCode = url.searchParams.get("discount");

      if (refCode && discountCode) {
        // Fetch partner details
        try {
          const response = await fetch(`/api/referral/validate?ref=${refCode}&discount=${discountCode}`);
          const result = await response.json();
          
          if (result.success) {
            const discount: ReferralDiscount = {
              code: result.data.discountCode,
              type: 'FIXED_AMOUNT',
              value: result.data.discountAmount,
              partnerName: result.data.partnerName,
              isActive: true
            };
            
            setReferralDiscount(discount);
            
            // Store in cookies for persistence
            document.cookie = `referral_discount=${JSON.stringify(discount)}; path=/; max-age=${60 * 60 * 24 * 30}`;
            
            // Clean URL
            url.searchParams.delete("ref");
            url.searchParams.delete("discount");
            window.history.replaceState({}, "", url.toString());
          }
        } catch (error) {
          console.error("Error validating referral:", error);
        }
      } else {
        // Check for existing referral in cookies
        const cookieMatch = document.cookie.match(/(?:^|; )referral_discount=([^;]*)/);
        if (cookieMatch && cookieMatch[1]) {
          try {
            const discount = JSON.parse(decodeURIComponent(cookieMatch[1]));
            setReferralDiscount(discount);
          } catch (error) {
            console.error("Error parsing referral discount cookie:", error);
          }
        }
      }
    };

    loadReferralFromCookies();
  }, []);

  const applyReferralToPrice = (originalPrice: number): number => {
    if (!referralDiscount || !referralDiscount.isActive) {
      return originalPrice;
    }

    if (referralDiscount.type === 'FIXED_AMOUNT') {
      return Math.max(0, originalPrice - referralDiscount.value);
    } else if (referralDiscount.type === 'PERCENTAGE') {
      return Math.max(0, originalPrice - (originalPrice * referralDiscount.value / 100));
    }

    return originalPrice;
  };

  const getReferralSavings = (originalPrice: number): number => {
    if (!referralDiscount || !referralDiscount.isActive) {
      return 0;
    }

    return originalPrice - applyReferralToPrice(originalPrice);
  };

  const clearReferral = () => {
    setReferralDiscount(null);
    // Clear cookie
    document.cookie = "referral_discount=; path=/; max-age=0";
  };

  const value: ReferralContextType = {
    referralDiscount,
    setReferralDiscount,
    applyReferralToPrice,
    getReferralSavings,
    isReferralActive: !!referralDiscount?.isActive,
    clearReferral
  };

  return (
    <ReferralContext.Provider value={value}>
      {children}
    </ReferralContext.Provider>
  );
}

export function useReferral() {
  const context = useContext(ReferralContext);
  if (context === undefined) {
    throw new Error("useReferral must be used within a ReferralProvider");
  }
  return context;
}
