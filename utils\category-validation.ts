/**
 * Category validation utilities to prevent misclassification
 */

export interface CategoryValidationResult {
  isValid: boolean;
  suggestedCategory?: string;
  confidence: number;
  warnings: string[];
}

export interface ProductAnalysis {
  type?: string;
  name?: string;
  description?: string;
  brand?: string;
  tags?: string[];
}

/**
 * Validate if a product is correctly categorized and suggest corrections
 */
export function validateProductCategory(
  productData: ProductAnalysis,
  assignedCategoryName: string,
  availableCategories: Array<{ id: string; name: string }>
): CategoryValidationResult {
  const warnings: string[] = [];
  let confidence = 100;
  let suggestedCategory: string | undefined;

  const productType = productData.type?.toLowerCase() || "";
  const productName = productData.name?.toLowerCase() || "";
  const description = productData.description?.toLowerCase() || "";
  const brand = productData.brand?.toLowerCase() || "";
  const tags = productData.tags?.map(tag => tag.toLowerCase()) || [];

  const combinedText = `${productType} ${productName} ${description} ${brand} ${tags.join(' ')}`;
  const categoryName = assignedCategoryName.toLowerCase();

  // Define category validation rules
  const validationRules = [
    {
      categoryPattern: /intimate|underwear/i,
      validKeywords: ['underwear', 'boxer', 'brief', 'panty', 'bra', 'lingerie', 'intimate'],
      invalidKeywords: ['bag', 'handbag', 'purse', 'backpack', 'sneaker', 'shoe', 'hat', 'cap'],
      errorMessage: "This appears to be a bag or accessory, not intimate wear"
    },
    {
      categoryPattern: /sneaker|shoe|footwear/i,
      validKeywords: ['sneaker', 'shoe', 'boot', 'sandal', 'trainer', 'runner', 'footwear'],
      invalidKeywords: ['bag', 'handbag', 'shirt', 'hoodie', 'jacket', 'pants'],
      errorMessage: "This appears to be clothing or accessories, not footwear"
    },
    {
      categoryPattern: /fashion|clothing|apparel/i,
      validKeywords: ['shirt', 'hoodie', 'jacket', 'dress', 'pants', 'jeans', 'sweater', 'top', 'bottom'],
      invalidKeywords: ['sneaker', 'shoe', 'bag', 'handbag', 'hat', 'cap'],
      errorMessage: "This appears to be footwear or accessories, not clothing"
    },
    {
      categoryPattern: /accessor|bag/i,
      validKeywords: ['bag', 'handbag', 'purse', 'backpack', 'tote', 'clutch', 'wallet', 'watch', 'jewelry'],
      invalidKeywords: ['sneaker', 'shoe', 'shirt', 'hoodie', 'underwear'],
      errorMessage: "This appears to be clothing or footwear, not an accessory"
    }
  ];

  // Check each validation rule
  for (const rule of validationRules) {
    if (rule.categoryPattern.test(categoryName)) {
      // Check for invalid keywords
      const hasInvalidKeywords = rule.invalidKeywords.some(keyword => 
        combinedText.includes(keyword)
      );
      
      if (hasInvalidKeywords) {
        confidence -= 50;
        warnings.push(rule.errorMessage);
        
        // Suggest correct category
        const correctCategory = findCorrectCategory(combinedText, availableCategories);
        if (correctCategory) {
          suggestedCategory = correctCategory.id;
        }
      }
      
      // Check for valid keywords
      const hasValidKeywords = rule.validKeywords.some(keyword => 
        combinedText.includes(keyword)
      );
      
      if (!hasValidKeywords && !hasInvalidKeywords) {
        confidence -= 20;
        warnings.push(`Product type unclear for ${categoryName} category`);
      }
    }
  }

  // Specific misclassification checks
  if (categoryName.includes('intimate') && (
    combinedText.includes('bag') || 
    combinedText.includes('handbag') || 
    combinedText.includes('purse')
  )) {
    confidence = 0;
    warnings.push("CRITICAL: Bag/handbag incorrectly classified as intimate wear");
    suggestedCategory = findCategoryByKeywords(['accessories', 'bags'], availableCategories)?.id;
  }

  if (categoryName.includes('sneaker') && (
    combinedText.includes('shirt') || 
    combinedText.includes('hoodie') || 
    combinedText.includes('jacket')
  )) {
    confidence = 0;
    warnings.push("CRITICAL: Clothing item incorrectly classified as sneaker");
    suggestedCategory = findCategoryByKeywords(['fashion', 'clothing', 'apparel'], availableCategories)?.id;
  }

  return {
    isValid: confidence >= 70,
    suggestedCategory,
    confidence,
    warnings
  };
}

/**
 * Find the correct category based on product content
 */
function findCorrectCategory(
  combinedText: string,
  availableCategories: Array<{ id: string; name: string }>
): { id: string; name: string } | null {
  const categoryKeywords = [
    { keywords: ['bag', 'handbag', 'purse', 'backpack'], categories: ['accessories', 'bags'] },
    { keywords: ['sneaker', 'shoe', 'boot'], categories: ['sneakers', 'footwear'] },
    { keywords: ['shirt', 'hoodie', 'jacket'], categories: ['fashion', 'clothing', 'apparel'] },
    { keywords: ['hat', 'cap', 'beanie'], categories: ['headwear', 'accessories'] },
    { keywords: ['watch', 'jewelry'], categories: ['accessories', 'jewelry'] }
  ];

  for (const mapping of categoryKeywords) {
    const hasKeyword = mapping.keywords.some(keyword => combinedText.includes(keyword));
    if (hasKeyword) {
      return findCategoryByKeywords(mapping.categories, availableCategories);
    }
  }

  return null;
}

/**
 * Find category by matching keywords in category names
 */
function findCategoryByKeywords(
  keywords: string[],
  availableCategories: Array<{ id: string; name: string }>
): { id: string; name: string } | null {
  for (const keyword of keywords) {
    const category = availableCategories.find(cat =>
      cat.name.toLowerCase().includes(keyword.toLowerCase())
    );
    if (category) {
      return category;
    }
  }
  return null;
}

/**
 * Get category confidence score based on product analysis
 */
export function getCategoryConfidenceScore(
  productData: ProductAnalysis,
  categoryName: string
): number {
  const validation = validateProductCategory(productData, categoryName, []);
  return validation.confidence;
}
