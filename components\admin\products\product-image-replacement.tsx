"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Replace,
  Upload,
  X,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Image as ImageIcon,
  Trash2,
  Plus,
  Move,
} from "lucide-react";
import { UploadDropzone } from "@/lib/uploadthing";
import { toast } from "react-hot-toast";

interface ProductImageReplacementProps {
  productId: string;
  images: string[];
  onImagesUpdated: (newImages: string[]) => void;
  className?: string;
}

interface ReplacementState {
  isOpen: boolean;
  imageIndex: number;
  currentImageUrl: string;
  newImageUrl: string;
  isUploading: boolean;
  isReplacing: boolean;
}

export default function ProductImageReplacement({
  productId,
  images,
  onImagesUpdated,
  className = "",
}: ProductImageReplacementProps) {
  const [replacement, setReplacement] = useState<ReplacementState>({
    isOpen: false,
    imageIndex: -1,
    currentImageUrl: "",
    newImageUrl: "",
    isUploading: false,
    isReplacing: false,
  });

  const [error, setError] = useState<string>("");
  const [isAddingImage, setIsAddingImage] = useState(false);
  const [isUploadingNew, setIsUploadingNew] = useState(false);
  const [pendingImages, setPendingImages] = useState<string[]>([]);
  const [recentlyAdded, setRecentlyAdded] = useState<number>(0);

  const openReplacement = (index: number, imageUrl: string) => {
    setReplacement({
      isOpen: true,
      imageIndex: index,
      currentImageUrl: imageUrl,
      newImageUrl: "",
      isUploading: false,
      isReplacing: false,
    });
    setError("");
  };

  const closeReplacement = () => {
    setReplacement({
      isOpen: false,
      imageIndex: -1,
      currentImageUrl: "",
      newImageUrl: "",
      isUploading: false,
      isReplacing: false,
    });
    setError("");
  };

  const handleImageUpload = (res: any[]) => {
    console.log("Upload response:", res);
    if (res && res.length > 0) {
      const uploadedFile = res[0];
      console.log("Uploaded file:", uploadedFile);
      if (uploadedFile && uploadedFile.url) {
        setReplacement(prev => ({
          ...prev,
          newImageUrl: uploadedFile.url,
          isUploading: false,
        }));
        toast.success("Image uploaded successfully!");
      } else {
        setError("Upload completed but no URL received");
        toast.error("Upload failed - no URL received");
        setReplacement(prev => ({ ...prev, isUploading: false }));
      }
    } else {
      setError("Upload completed but no files received");
      toast.error("Upload failed - no files received");
      setReplacement(prev => ({ ...prev, isUploading: false }));
    }
  };

  const handleUploadError = (error: Error) => {
    setReplacement(prev => ({ ...prev, isUploading: false }));
    setError(`Upload failed: ${error.message}`);
    toast.error("Upload failed. Please try again.");
  };

  const handleReplaceImage = async () => {
    if (!replacement.newImageUrl) {
      setError("Please upload a new image first");
      toast.error("Please upload a new image first");
      return;
    }

    console.log("Replacing image:", {
      productId,
      imageIndex: replacement.imageIndex,
      newImageUrl: replacement.newImageUrl,
    });

    setReplacement(prev => ({ ...prev, isReplacing: true }));
    setError("");

    try {
      const response = await fetch(`/api/admin/products/${productId}/replace-image`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageIndex: replacement.imageIndex,
          newImageUrl: replacement.newImageUrl,
        }),
      });

      console.log("Replace response status:", response.status);
      const result = await response.json();
      console.log("Replace response:", result);

      if (result.success) {
        // Update the images array locally
        const newImages = [...images];
        newImages[replacement.imageIndex] = replacement.newImageUrl;
        onImagesUpdated(newImages);

        toast.success("Image replaced successfully!");
        closeReplacement();
      } else {
        const errorMsg = result.error || "Failed to replace image";
        setError(errorMsg);
        toast.error(errorMsg);
        console.error("Replace failed:", result);
      }
    } catch (error) {
      console.error("Error replacing image:", error);
      const errorMsg = "Failed to replace image. Please try again.";
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setReplacement(prev => ({ ...prev, isReplacing: false }));
    }
  };

  const handleRemoveImage = async (index: number) => {
    if (images.length <= 1) {
      toast.error("Cannot remove the last image. Products must have at least one image.");
      return;
    }

    if (!confirm("Are you sure you want to remove this image?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/products/${productId}/remove-image`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageIndex: index,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Update the images array locally
        const newImages = images.filter((_, i) => i !== index);
        onImagesUpdated(newImages);

        toast.success("Image removed successfully!");
      } else {
        toast.error(result.error || "Failed to remove image");
      }
    } catch (error) {
      console.error("Error removing image:", error);
      toast.error("Failed to remove image");
    }
  };



  const handleNewImageUpload = (res: any[]) => {
    if (res && res.length > 0) {
      const uploadedUrls = res.map(file => file.url);

      // Check if adding these images would exceed the 7-image limit
      const totalImagesAfterUpload = images.length + pendingImages.length + uploadedUrls.length;

      if (totalImagesAfterUpload > 10) {
        const remainingSlots = 10 - (images.length + pendingImages.length);

        if (remainingSlots > 0) {
          // Only add as many images as we have slots for
          const limitedUrls = uploadedUrls.slice(0, remainingSlots);
          setPendingImages(prev => [...prev, ...limitedUrls]);
          toast(`Only ${remainingSlots} image(s) were added. Maximum 10 images allowed per product.`, {
            icon: '⚠️',
            style: {
              background: '#FEF3C7',
              color: '#92400E',
            },
          });
        } else {
          toast.error('Cannot add more images. Maximum 10 images allowed per product.');
        }
      } else {
        setPendingImages(prev => [...prev, ...uploadedUrls]);
        toast.success(`${res.length} image(s) uploaded successfully!`);
      }
      setIsUploadingNew(false);
    }
  };

  const handleNewImageUploadError = (error: Error) => {
    setError(`Upload failed: ${error.message}`);
    toast.error("Upload failed. Please try again.");
    setIsUploadingNew(false);
  };

  const handleAddAllPendingImages = async () => {
    if (pendingImages.length === 0) {
      toast.error("No images to add");
      return;
    }

    // Check if adding all pending images would exceed the 7-image limit
    const totalAfterAdding = images.length + pendingImages.length;

    if (totalAfterAdding > 10) {
      const remainingSlots = 10 - images.length;
      if (remainingSlots <= 0) {
        toast.error('Cannot add images. Product already has the maximum of 10 images.');
        return;
      }

      const confirmMessage = `Adding all ${pendingImages.length} images would exceed the 10-image limit. Only ${remainingSlots} image(s) can be added. Continue?`;
      if (!confirm(confirmMessage)) {
        return;
      }

      // Limit the images to add
      const imagesToAdd = pendingImages.slice(0, remainingSlots);
      const remainingPending = pendingImages.slice(remainingSlots);

      setIsAddingImage(true);
      setError("");

      try {
        // Add limited images one by one
        const addPromises = imagesToAdd.map(async (imageUrl) => {
          const response = await fetch(`/api/admin/products/${productId}/add-image`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              imageUrl: imageUrl,
            }),
          });

          const result = await response.json();
          if (!result.success) {
            throw new Error(result.error || "Failed to add image");
          }
          return imageUrl;
        });

        const addedImages = await Promise.all(addPromises);

        // Update the images array locally
        const newImages = [...images, ...addedImages];
        onImagesUpdated(newImages);

        toast.success(`${addedImages.length} image(s) added successfully! ${remainingPending.length} image(s) remain pending.`);
        setPendingImages(remainingPending);
      } catch (error) {
        console.error("Error adding images:", error);
        setError("Failed to add some images. Please try again.");
        toast.error("Failed to add images");
      } finally {
        setIsAddingImage(false);
      }
    } else {
      // Normal flow - add all images
      setIsAddingImage(true);
      setError("");

      try {
        // Add all pending images one by one
        const addPromises = pendingImages.map(async (imageUrl) => {
          const response = await fetch(`/api/admin/products/${productId}/add-image`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              imageUrl: imageUrl,
            }),
          });

          const result = await response.json();
          if (!result.success) {
            throw new Error(result.error || "Failed to add image");
          }
          return imageUrl;
        });

        const addedImages = await Promise.all(addPromises);

        // Update the images array locally
        const newImages = [...images, ...addedImages];
        onImagesUpdated(newImages);

        toast.success(`${addedImages.length} image(s) added successfully!`);
        setPendingImages([]);
        setRecentlyAdded(addedImages.length);

        // Clear the success state after 3 seconds
        setTimeout(() => {
          setRecentlyAdded(0);
        }, 3000);
      } catch (error) {
        console.error("Error adding images:", error);
        setError("Failed to add some images. Please try again.");
        toast.error("Failed to add images");
      } finally {
        setIsAddingImage(false);
      }
    }
  };

  const removePendingImage = (index: number) => {
    setPendingImages(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Product Images ({images.length}/7)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Success Banner */}
          {recentlyAdded > 0 && (
            <Alert className="mb-4 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <strong>Success!</strong> {recentlyAdded} image(s) have been added to the product and are now visible in the gallery above.
              </AlertDescription>
            </Alert>
          )}
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-900 mb-2">📸 How to Manage Product Images:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Add new images:</strong> Use the "Add New Images" card below (supports multiple files)</li>
                <li>• <strong>Replace images:</strong> Hover over any image and click "Replace"</li>
                <li>• <strong>Remove images:</strong> Hover over any image and click the trash icon</li>
                <li>• <strong>Maximum:</strong> 10 images per product</li>
              </ul>
            </div>
            
            {images.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No images available</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {images.map((imageUrl, index) => (
                  <div key={index} className="relative group">
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border">
                      <img
                        src={imageUrl}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Image overlay with actions */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => openReplacement(index, imageUrl)}
                        className="bg-white/90 hover:bg-white text-black"
                      >
                        <Replace className="h-4 w-4 mr-1" />
                        Replace
                      </Button>

                      {images.length > 1 && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleRemoveImage(index)}
                          className="bg-red-600/90 hover:bg-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    {/* Image position badge */}
                    <Badge
                      variant="secondary"
                      className="absolute top-2 left-2 bg-white/90 text-black"
                    >
                      {index + 1}
                    </Badge>
                  </div>
                ))}

                {/* Add New Images Card */}
                {images.length >= 10 ? (
                  <div className="aspect-square bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center">
                    <div className="text-center p-4">
                      <ImageIcon className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                      <p className="text-sm text-gray-500 mb-1">Maximum Reached</p>
                      <p className="text-xs text-gray-400">10/10 images</p>
                      <p className="text-xs text-gray-400 mt-2">Remove an image to add new ones</p>
                    </div>
                  </div>
                ) : (
                  <div className={`aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-all duration-300 ${
                    images.length >= 9
                      ? 'border-orange-400 hover:border-orange-500 bg-orange-50 hover:bg-orange-100'
                      : 'border-blue-400 hover:border-blue-500 bg-blue-50 hover:bg-blue-100'
                  }`}>
                    <div className="text-center p-4 w-full">
                      <div className={`rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3 ${
                        images.length >= 9 ? 'bg-orange-200' : 'bg-blue-200'
                      }`}>
                        <Plus className={`h-6 w-6 ${images.length >= 9 ? 'text-orange-600' : 'text-blue-600'}`} />
                      </div>
                      <p className={`text-sm font-medium mb-1 ${images.length >= 9 ? 'text-orange-700' : 'text-blue-700'}`}>
                        📤 Add New Images
                      </p>
                      <p className="text-xs text-gray-600 mb-2">Drag & drop or click to select multiple files</p>
                      {images.length >= 9 && (
                        <p className="text-xs text-orange-600 mb-2 font-medium">⚠️ Only {10 - images.length} slot(s) remaining</p>
                      )}
                      <UploadDropzone
                        endpoint="productImageUploader"
                        onClientUploadComplete={handleNewImageUpload}
                        onUploadError={handleNewImageUploadError}
                        onUploadBegin={() => setIsUploadingNew(true)}
                        className="border-0 p-2"
                        appearance={{
                          container: "w-full h-auto",
                          uploadIcon: "hidden",
                          label: "text-xs",
                          allowedContent: "text-xs",
                        }}
                      />
                      {isUploadingNew && (
                        <div className="flex items-center justify-center gap-2 mt-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-xs text-gray-600">Uploading...</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Pending Images Section */}
            {pendingImages.length > 0 && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-blue-800">
                      Pending Images ({pendingImages.length})
                    </h4>
                    <p className="text-xs text-blue-600">
                      Total after adding: {images.length + pendingImages.length}/10
                      {images.length + pendingImages.length > 10 && (
                        <span className="text-orange-600 ml-1">⚠️ Exceeds limit</span>
                      )}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={handleAddAllPendingImages}
                      disabled={isAddingImage}
                      className="bg-green-600 hover:bg-green-700 animate-pulse"
                    >
                      {isAddingImage ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Adding to Product...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4 mr-1" />
                          Add All to Product
                        </>
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setPendingImages([])}
                      disabled={isAddingImage}
                    >
                      Clear All
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                  {pendingImages.map((imageUrl, index) => (
                    <div key={index} className="relative group">
                      <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border">
                        <img
                          src={imageUrl}
                          alt={`Pending image ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removePendingImage(index)}
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <Badge
                        variant="secondary"
                        className="absolute bottom-1 left-1 bg-white/90 text-black text-xs"
                      >
                        {index + 1}
                      </Badge>
                    </div>
                  ))}
                </div>
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800 font-medium">
                    📋 Next Step: Click "Add All to Product" to save these images to the product
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Images are uploaded but not yet saved to the product. Click the green button above to complete the process.
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Replacement Dialog */}
      <Dialog open={replacement.isOpen} onOpenChange={closeReplacement}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Replace Product Image</DialogTitle>
            <DialogDescription>
              Upload a new image to replace the current one. The new image will maintain the same position in the gallery.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Current Image */}
            <div>
              <h4 className="font-medium mb-2">Current Image (Position {replacement.imageIndex + 1})</h4>
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden border max-w-sm">
                <img
                  src={replacement.currentImageUrl}
                  alt="Current image"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* New Image Upload */}
            <div>
              <h4 className="font-medium mb-2">New Image</h4>
              {replacement.newImageUrl ? (
                <div className="space-y-2">
                  <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden border max-w-sm">
                    <img
                      src={replacement.newImageUrl}
                      alt="New image"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">New image ready for replacement</span>
                  </div>
                </div>
              ) : (
                <UploadDropzone
                  endpoint="productImageUploader"
                  onClientUploadComplete={handleImageUpload}
                  onUploadError={handleUploadError}
                  onUploadBegin={() => setReplacement(prev => ({ ...prev, isUploading: true }))}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6"
                />
              )}
            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeReplacement} disabled={replacement.isReplacing}>
              Cancel
            </Button>
            <Button 
              onClick={handleReplaceImage} 
              disabled={!replacement.newImageUrl || replacement.isReplacing}
            >
              {replacement.isReplacing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Replacing...
                </>
              ) : (
                <>
                  <Replace className="h-4 w-4 mr-2" />
                  Replace Image
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
