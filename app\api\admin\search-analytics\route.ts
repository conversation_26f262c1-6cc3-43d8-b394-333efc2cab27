import { NextRequest, NextResponse } from 'next/server';
import { getSearchInsights, logSearch } from '@/lib/search-analytics';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;

    const insights = await getSearchInsights(startDate, endDate);

    return NextResponse.json({
      success: true,
      data: insights
    });
  } catch (error) {
    console.error('Error getting search analytics:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get search analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    await logSearch({
      query: body.query,
      resultsCount: body.resultsCount,
      clickedProductId: body.clickedProductId,
      userAgent: request.headers.get('user-agent') || undefined,
      timestamp: new Date(),
      searchDuration: body.searchDuration,
      filters: body.filters
    });

    return NextResponse.json({
      success: true,
      message: 'Search analytics logged successfully'
    });
  } catch (error) {
    console.error('Error logging search analytics:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to log search analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
