import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// POST /api/stock-notifications - Create a stock notification request
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, productId, size } = body;

    // Validate required fields
    if (!email || !productId) {
      return NextResponse.json(
        { success: false, error: "Email and product ID are required" },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: "Invalid email format" },
        { status: 400 }
      );
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, stock: true, sizes: true }
    });

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if size is valid for this product (if size is specified)
    if (size && !product.sizes.includes(size)) {
      return NextResponse.json(
        { success: false, error: "Invalid size for this product" },
        { status: 400 }
      );
    }

    // Check if notification already exists
    const existingNotification = await prisma.stockNotification.findFirst({
      where: {
        email,
        productId,
        size: size || null,
        isActive: true
      }
    });

    if (existingNotification) {
      return NextResponse.json(
        { success: false, error: "You're already subscribed to notifications for this item" },
        { status: 400 }
      );
    }

    // Create the stock notification
    const notification = await prisma.stockNotification.create({
      data: {
        email,
        productId,
        size: size || null,
        isActive: true,
        createdAt: new Date()
      }
    });

    const response: ApiResponse<typeof notification> = {
      success: true,
      data: notification,
      message: "Stock notification created successfully"
    };

    console.log(`[STOCK_NOTIFICATION] Created for ${email} - Product: ${product.name}${size ? ` (Size: ${size})` : ''}`);

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error creating stock notification:", error);
    
    return NextResponse.json(
      { success: false, error: "Failed to create stock notification" },
      { status: 500 }
    );
  }
}

// GET /api/stock-notifications - Get stock notifications (admin only)
export async function GET(request: NextRequest) {
  try {
    // This would typically require admin authentication
    // For now, we'll return a simple response
    
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");

    const where: any = {
      isActive: true
    };

    if (productId) {
      where.productId = productId;
    }

    const notifications = await prisma.stockNotification.findMany({
      where,
      include: {
        product: {
          select: {
            name: true,
            stock: true
          }
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    const response: ApiResponse<typeof notifications> = {
      success: true,
      data: notifications
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching stock notifications:", error);
    
    return NextResponse.json(
      { success: false, error: "Failed to fetch stock notifications" },
      { status: 500 }
    );
  }
}
