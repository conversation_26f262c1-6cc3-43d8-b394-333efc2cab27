/**
 * Utility functions for generating combo deal images
 */

export interface ComboImageOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  borderRadius?: number;
  padding?: number;
  logoUrl?: string;
  watermarkText?: string;
}

export interface ProductImage {
  url: string;
  name: string;
  brand: string;
  price: number;
}

/**
 * Generate a combo deal image by composing multiple product images
 */
export async function generateComboImage(
  products: ProductImage[],
  comboName: string,
  originalPrice: number,
  comboPrice: number,
  options: ComboImageOptions = {}
): Promise<string> {
  const {
    width = 800,
    height = 600,
    backgroundColor = '#ffffff',
    borderRadius = 12,
    padding = 20,
    logoUrl = '/logo.png',
    watermarkText = 'RIVV COMBO DEAL'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    canvas.width = width;
    canvas.height = height;

    // Set background
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, width, height);

    // Add border radius effect by clipping
    ctx.save();
    ctx.beginPath();
    ctx.roundRect(0, 0, width, height, borderRadius);
    ctx.clip();

    let loadedImages = 0;
    const totalImages = products.length + 1; // +1 for logo
    const productImages: HTMLImageElement[] = [];
    const logoImage = new Image();

    const checkAllLoaded = () => {
      if (loadedImages === totalImages) {
        drawComboImage();
      }
    };

    const drawComboImage = () => {
      try {
        // Clear and redraw background
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);

        // Calculate layout
        const productCount = products.length;
        const cols = Math.ceil(Math.sqrt(productCount));
        const rows = Math.ceil(productCount / cols);
        
        const availableWidth = width - (padding * 2);
        const availableHeight = height - 200; // Reserve space for text and logo
        
        const productWidth = (availableWidth - (cols - 1) * 10) / cols;
        const productHeight = (availableHeight - (rows - 1) * 10) / rows;

        // Draw product images
        productImages.forEach((img, index) => {
          if (img.complete) {
            const col = index % cols;
            const row = Math.floor(index / cols);
            
            const x = padding + col * (productWidth + 10);
            const y = padding + row * (productHeight + 10);
            
            // Draw product image with aspect ratio preservation
            const aspectRatio = img.width / img.height;
            let drawWidth = productWidth;
            let drawHeight = productHeight;
            
            if (aspectRatio > 1) {
              drawHeight = productWidth / aspectRatio;
            } else {
              drawWidth = productHeight * aspectRatio;
            }
            
            const drawX = x + (productWidth - drawWidth) / 2;
            const drawY = y + (productHeight - drawHeight) / 2;
            
            // Add subtle shadow
            ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
            
            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
          }
        });

        // Draw combo title
        ctx.fillStyle = '#1b1f3b'; // RIVV navy blue (hardcoded for canvas)
        ctx.font = 'bold 32px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(comboName, width / 2, height - 140);

        // Draw price information
        const discount = originalPrice - comboPrice;
        const discountPercent = ((discount / originalPrice) * 100).toFixed(0);

        // Original price (crossed out)
        ctx.fillStyle = '#9ca3af';
        ctx.font = '24px Arial, sans-serif';
        const originalText = `M${originalPrice.toFixed(2)}`;
        const originalTextWidth = ctx.measureText(originalText).width;
        ctx.fillText(originalText, width / 2 - 60, height - 100);
        
        // Cross out line
        ctx.strokeStyle = '#9ca3af';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(width / 2 - 60 - originalTextWidth / 2, height - 105);
        ctx.lineTo(width / 2 - 60 + originalTextWidth / 2, height - 105);
        ctx.stroke();

        // Combo price
        ctx.fillStyle = '#059669'; // Green
        ctx.font = 'bold 28px Arial, sans-serif';
        ctx.fillText(`M${comboPrice.toFixed(2)}`, width / 2 + 60, height - 100);

        // Savings
        ctx.fillStyle = '#dc2626'; // Red
        ctx.font = 'bold 20px Arial, sans-serif';
        ctx.fillText(`Save ${discountPercent}%`, width / 2, height - 70);

        // Draw logo
        if (logoImage.complete) {
          const logoSize = 40;
          ctx.drawImage(logoImage, width - logoSize - 20, 20, logoSize, logoSize);
        }

        // Draw watermark
        ctx.fillStyle = 'rgba(27, 31, 59, 0.1)'; // RIVV navy with transparency
        ctx.font = 'bold 16px Arial, sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText(watermarkText, width - 20, height - 20);

        // Convert to blob and create URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve(url);
          } else {
            reject(new Error('Failed to create image blob'));
          }
        }, 'image/png', 0.9);

      } catch (error) {
        reject(error);
      }
    };

    // Load product images
    products.forEach((product, index) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        loadedImages++;
        checkAllLoaded();
      };
      img.onerror = () => {
        console.warn(`Failed to load product image: ${product.url}`);
        loadedImages++;
        checkAllLoaded();
      };
      img.src = product.url;
      productImages[index] = img;
    });

    // Load logo
    logoImage.crossOrigin = 'anonymous';
    logoImage.onload = () => {
      loadedImages++;
      checkAllLoaded();
    };
    logoImage.onerror = () => {
      console.warn('Failed to load logo');
      loadedImages++;
      checkAllLoaded();
    };
    logoImage.src = logoUrl;
  });
}

/**
 * Upload generated combo image to storage
 */
export async function uploadComboImage(imageBlob: Blob, comboId: string): Promise<string> {
  try {
    const formData = new FormData();
    formData.append('file', imageBlob, `combo-${comboId}.png`);

    const response = await fetch('/api/admin/combo-deals/upload-image', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    
    if (result.success) {
      return result.data.url;
    } else {
      throw new Error(result.error || 'Failed to upload image');
    }
  } catch (error) {
    console.error('Error uploading combo image:', error);
    throw error;
  }
}

/**
 * Generate and upload combo image in one step
 */
export async function generateAndUploadComboImage(
  products: ProductImage[],
  comboName: string,
  originalPrice: number,
  comboPrice: number,
  comboId: string,
  options?: ComboImageOptions
): Promise<string> {
  try {
    // Generate the image
    const imageUrl = await generateComboImage(
      products,
      comboName,
      originalPrice,
      comboPrice,
      options
    );

    // Convert URL to blob
    const response = await fetch(imageUrl);
    const blob = await response.blob();

    // Upload to storage
    const uploadedUrl = await uploadComboImage(blob, comboId);

    // Clean up the temporary URL
    URL.revokeObjectURL(imageUrl);

    return uploadedUrl;
  } catch (error) {
    console.error('Error generating and uploading combo image:', error);
    throw error;
  }
}
