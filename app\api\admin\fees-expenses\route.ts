import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";
import { getFeeSettings } from "@/lib/fee-utils";

// GET /api/admin/fees-expenses - Get comprehensive fee and expense data
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get all orders with their items and fee breakdowns
    const orders = await prisma.order.findMany({
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                brand: true,
              },
            },
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get fee settings for calculations
    const feeSettings = await getFeeSettings();

    // Calculate comprehensive fee statistics
    let totalShippingFees = 0;
    let totalDeliveryFees = 0;
    let totalLateCollectionFees = 0;
    let totalBulkDeliveryDiscounts = 0;
    let totalFeesPaid = 0;
    let totalFeesOwed = 0;
    let totalRevenue = 0;
    let totalCostPrice = 0;
    let totalProfit = 0;

    // Process each order
    const ordersWithFees = orders.map(order => {
      const orderShippingFees = order.orderItems.reduce((sum, item) => 
        sum + ((item.shippingFee || 0) * item.quantity), 0);
      const orderLateFees = order.orderItems.reduce((sum, item) => 
        sum + ((item.lateCollectionFee || 0) * item.quantity), 0);
      const orderDeliveryFee = order.deliveryFee || 0;
      const orderRevenue = order.totalAmount;
      const orderCostPrice = order.totalCostPrice || 0;
      const orderProfit = order.totalProfit || 0;

      // Calculate bulk delivery discount (difference between regular and bulk rates)
      let bulkDiscount = 0;
      if (order.isBulkDelivery) {
        const totalShoes = order.orderItems.reduce((sum, item) => sum + item.quantity, 0);
        const regularFee = feeSettings.defaultDeliveryFee * totalShoes;
        const bulkFee = feeSettings.defaultBulkDeliveryFee * totalShoes;
        bulkDiscount = regularFee - bulkFee;
      }

      // Accumulate totals
      totalShippingFees += orderShippingFees;
      totalDeliveryFees += orderDeliveryFee;
      totalLateCollectionFees += orderLateFees;
      totalBulkDeliveryDiscounts += bulkDiscount;
      totalRevenue += orderRevenue;
      totalCostPrice += orderCostPrice;
      totalProfit += orderProfit;

      if (order.deliveryFeePaid) {
        totalFeesPaid += orderDeliveryFee;
      } else {
        totalFeesOwed += orderDeliveryFee;
      }

      return {
        id: order.id,
        orderNumber: order.orderNumber,
        customerName: order.user?.name || "Unknown Customer",
        status: order.status,
        createdAt: order.createdAt.toISOString(),
        deliveredAt: order.deliveredAt?.toISOString() || null,
        totalAmount: order.totalAmount,
        totalCostPrice: orderCostPrice,
        totalProfit: orderProfit,
        shippingFees: orderShippingFees,
        deliveryFee: orderDeliveryFee,
        lateCollectionFees: orderLateFees,
        bulkDeliveryDiscount: bulkDiscount,
        isBulkDelivery: order.isBulkDelivery,
        deliveryFeePaid: order.deliveryFeePaid,
        deliveryFeePaidAt: order.deliveryFeePaidAt?.toISOString() || null,
        shoesCount: order.orderItems.reduce((sum, item) => sum + item.quantity, 0),
        items: order.orderItems
          .filter(item => item.product !== null)
          .map(item => ({
            id: item.id,
            productName: item.product!.name,
            productBrand: item.product!.brand,
          quantity: item.quantity,
          price: item.price,
          costPrice: item.costPrice || 0,
          shippingFee: item.shippingFee || 0,
          lateCollectionFee: item.lateCollectionFee || 0,
          totalCost: item.totalCost || 0,
          profit: item.profit || 0,
        })),
      };
    });

    // Calculate overall statistics
    const stats = {
      totalRevenue,
      totalCostPrice,
      totalProfit,
      totalShippingFees,
      totalDeliveryFees,
      totalLateCollectionFees,
      totalBulkDeliveryDiscounts,
      totalFeesPaid,
      totalFeesOwed,
      totalFeesOverall: totalShippingFees + totalDeliveryFees + totalLateCollectionFees,
      netProfitAfterFees: totalProfit - totalDeliveryFees,
      ordersCount: orders.length,
      deliveredOrdersCount: orders.filter(o => o.status === "DELIVERED").length,
      bulkDeliveriesCount: orders.filter(o => o.isBulkDelivery).length,
    };

    const response: ApiResponse<{ orders: typeof ordersWithFees; stats: typeof stats }> = {
      success: true,
      data: {
        orders: ordersWithFees,
        stats,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching fees and expenses:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch fees and expenses" },
      { status: 500 }
    );
  }
} 