"use client";

import { useReferral } from "@/contexts/referral-context";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Gift, X } from "lucide-react";
import { formatPrice } from "@/lib/product-utils";

export default function ReferralBanner() {
  const { referralDiscount, clearReferral, isReferralActive } = useReferral();

  if (!isReferralActive || !referralDiscount) {
    return null;
  }

  return (
    <Alert className="border-green-200 bg-green-50 mb-4">
      <Gift className="h-4 w-4 text-green-600" />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-green-800">
            🎉 Referral discount active! 
            {referralDiscount.partnerName && (
              <span className="font-medium"> via {referralDiscount.partnerName}</span>
            )}
          </span>
          <Badge className="bg-green-100 text-green-800 border-green-200">
            -{formatPrice(referralDiscount.value)} off each item
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearReferral}
          className="text-green-600 hover:text-green-800 hover:bg-green-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </AlertDescription>
    </Alert>
  );
}
