@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.25 0.12 250);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.95 0.02 220);
  --secondary-foreground: oklch(0.25 0.12 250);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.7 0.15 200);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.7 0.15 200);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.25 0.12 250);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.95 0.02 220);
  --sidebar-accent-foreground: oklch(0.25 0.12 250);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.7 0.15 200);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.6 0.12 250);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.6 0.12 250);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

html, body {
  overflow-x: hidden;
}

/* Prevent horizontal overflow on all elements */
* {
  box-sizing: border-box;
}

/* Ensure containers don't overflow */
.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl, .max-w-2xl, .max-w-xl {
  max-width: 100%;
  overflow-x: hidden;
}

/* Responsive images and media */
img, .responsive-img, video, iframe {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Prevent text overflow */
.text-overflow-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive text that breaks properly */
.break-words {
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* Hide scrollbar utility */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}

@media (max-width: 640px) {
  /* Prevent horizontal overflow on mobile */
  body {
    overflow-x: hidden !important;
    width: 100vw;
  }

  /* Responsive tables */
  .responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .responsive-table table {
    width: 100%;
    min-width: 600px;
  }

  /* Mobile-specific improvements */
  .mobile-optimized {
    padding: 0.75rem;
  }

  /* Improve touch targets on mobile */
  button, .btn, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile forms */
  .form-mobile {
    gap: 1rem;
  }

  /* Mobile-friendly dialog/modal positioning */
  .mobile-dialog {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  /* Prevent wide elements from causing overflow */
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl, .max-w-2xl, .max-w-xl {
    padding-left: 1rem;
    padding-right: 1rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* Ensure grid layouts don't overflow */
  .grid {
    overflow-x: hidden;
  }

  /* Fix button positioning that might cause overflow */
  .absolute.right-24 {
    right: 6rem;
  }

  /* Ensure product cards don't overflow */
  .product-card {
    max-width: 100%;
    overflow: hidden;
  }

  /* Fix long text in cards */
  .card-title, .product-name {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }
}

/* RIVV Brand Enhancements - Modern Vibrant Design */
.rivv-gradient-primary {
  background: linear-gradient(135deg, oklch(0.25 0.12 250) 0%, oklch(0.7 0.15 200) 100%);
}

.rivv-gradient-accent {
  background: linear-gradient(135deg, oklch(0.7 0.15 200) 0%, oklch(0.8 0.18 190) 100%);
}

.rivv-glow {
  box-shadow: 0 0 20px oklch(0.7 0.15 200 / 0.3);
}

.rivv-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rivv-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px oklch(0.25 0.12 250 / 0.15);
}

/* Enhanced button styles */
.btn-rivv-primary {
  background: linear-gradient(135deg, oklch(0.25 0.12 250) 0%, oklch(0.3 0.14 245) 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-rivv-primary:hover {
  background: linear-gradient(135deg, oklch(0.3 0.14 245) 0%, oklch(0.35 0.16 240) 100%);
  transform: translateY(-1px);
  box-shadow: 0 8px 20px oklch(0.25 0.12 250 / 0.25);
}

.btn-rivv-accent {
  background: linear-gradient(135deg, oklch(0.7 0.15 200) 0%, oklch(0.75 0.17 195) 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-rivv-accent:hover {
  background: linear-gradient(135deg, oklch(0.75 0.17 195) 0%, oklch(0.8 0.19 190) 100%);
  transform: translateY(-1px);
  box-shadow: 0 8px 20px oklch(0.7 0.15 200 / 0.25);
}
