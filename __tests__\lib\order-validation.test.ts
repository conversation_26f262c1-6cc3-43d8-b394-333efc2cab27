import { validateOrder, applyOrderCorrections } from '@/lib/order-validation'

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  default: {
    orderValidation: {
      create: jest.fn(),
    },
    product: {
      findUnique: jest.fn(),
    },
  },
}))

// Mock email service
jest.mock('@/lib/email', () => ({
  sendEmail: jest.fn(),
}))

describe('Order Validation', () => {
  const mockOrderData = {
    customerId: 'customer-123',
    items: [
      {
        productId: 'product-1',
        quantity: 2,
        price: 100,
        size: '9',
        color: 'Black'
      },
      {
        productId: 'product-2',
        quantity: 1,
        price: 200,
        size: '10',
        color: 'White'
      }
    ],
    deliveryFee: 90,
    discount: 0,
    subtotal: 400,
    total: 490
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('calculateOrderTotals', () => {
    it('calculates correct totals for regular order', () => {
      const result = calculateOrderTotals(mockOrderData)
      
      expect(result.subtotal).toBe(400) // (2 * 100) + (1 * 200)
      expect(result.deliveryFee).toBe(90)
      expect(result.total).toBe(490) // 400 + 90
      expect(result.discount).toBe(0)
    })

    it('applies bulk delivery discount for 5+ items', () => {
      const bulkOrderData = {
        ...mockOrderData,
        items: [
          ...mockOrderData.items,
          { productId: 'product-3', quantity: 3, price: 50, size: '8', color: 'Red' }
        ]
      }
      
      const result = calculateOrderTotals(bulkOrderData)
      
      // Should apply M60 bulk delivery fee instead of M90 per shoe
      expect(result.deliveryFee).toBe(60)
      expect(result.subtotal).toBe(550) // 400 + (3 * 50)
      expect(result.total).toBe(610) // 550 + 60
    })

    it('handles discount application correctly', () => {
      const discountOrderData = {
        ...mockOrderData,
        discount: 50
      }
      
      const result = calculateOrderTotals(discountOrderData)
      
      expect(result.subtotal).toBe(400)
      expect(result.discount).toBe(50)
      expect(result.deliveryFee).toBe(90)
      expect(result.total).toBe(440) // 400 - 50 + 90
    })

    it('prevents negative totals with large discounts', () => {
      const largeDiscountOrderData = {
        ...mockOrderData,
        discount: 500 // Larger than subtotal
      }
      
      const result = calculateOrderTotals(largeDiscountOrderData)
      
      expect(result.total).toBeGreaterThanOrEqual(result.deliveryFee)
    })
  })

  describe('validateOrderData', () => {
    it('validates correct order data without corrections', async () => {
      const result = await validateOrderData(mockOrderData)
      
      expect(result.isValid).toBe(true)
      expect(result.corrections).toHaveLength(0)
      expect(result.correctedTotal).toBe(490)
      expect(result.discrepancy).toBe(0)
    })

    it('detects and corrects delivery fee errors', async () => {
      const incorrectOrderData = {
        ...mockOrderData,
        deliveryFee: 180, // Should be 90 for 2 items
        total: 580 // Incorrect total
      }
      
      const result = await validateOrderData(incorrectOrderData)
      
      expect(result.isValid).toBe(false)
      expect(result.corrections).toHaveLength(1)
      expect(result.corrections[0].type).toBe('delivery_fee')
      expect(result.correctedTotal).toBe(490)
      expect(result.discrepancy).toBe(90)
    })

    it('detects and corrects calculation errors', async () => {
      const incorrectOrderData = {
        ...mockOrderData,
        total: 500 // Should be 490
      }
      
      const result = await validateOrderData(incorrectOrderData)
      
      expect(result.isValid).toBe(false)
      expect(result.corrections).toHaveLength(1)
      expect(result.corrections[0].type).toBe('calculation')
      expect(result.correctedTotal).toBe(490)
      expect(result.discrepancy).toBe(10)
    })

    it('handles multiple validation errors', async () => {
      const multiErrorOrderData = {
        ...mockOrderData,
        deliveryFee: 180, // Wrong delivery fee
        discount: -10, // Invalid negative discount
        total: 600 // Wrong total
      }
      
      const result = await validateOrderData(multiErrorOrderData)
      
      expect(result.isValid).toBe(false)
      expect(result.corrections.length).toBeGreaterThan(1)
      expect(result.discrepancy).toBeGreaterThan(0)
    })

    it('validates lay-buy calculations correctly', async () => {
      const layBuyOrderData = {
        ...mockOrderData,
        isLayBuy: true,
        layBuyUpfrontAmount: 294, // 60% of 490
        layBuyRemainingAmount: 196 // 40% of 490
      }
      
      const result = await validateOrderData(layBuyOrderData)
      
      expect(result.isValid).toBe(true)
      expect(result.corrections).toHaveLength(0)
    })

    it('detects incorrect lay-buy calculations', async () => {
      const incorrectLayBuyOrderData = {
        ...mockOrderData,
        isLayBuy: true,
        layBuyUpfrontAmount: 300, // Should be 294 (60% of 490)
        layBuyRemainingAmount: 190 // Should be 196 (40% of 490)
      }
      
      const result = await validateOrderData(incorrectLayBuyOrderData)
      
      expect(result.isValid).toBe(false)
      expect(result.corrections.some(c => c.type === 'lay_buy_error')).toBe(true)
    })

    it('handles empty order items', async () => {
      const emptyOrderData = {
        ...mockOrderData,
        items: []
      }
      
      const result = await validateOrderData(emptyOrderData)
      
      expect(result.isValid).toBe(false)
      expect(result.corrections.some(c => c.type === 'invalid_items')).toBe(true)
    })

    it('validates item quantities and prices', async () => {
      const invalidItemOrderData = {
        ...mockOrderData,
        items: [
          {
            productId: 'product-1',
            quantity: 0, // Invalid quantity
            price: -100, // Invalid price
            size: '9',
            color: 'Black'
          }
        ]
      }
      
      const result = await validateOrderData(invalidItemOrderData)
      
      expect(result.isValid).toBe(false)
      expect(result.corrections.some(c => c.type === 'invalid_items')).toBe(true)
    })
  })

  describe('Edge Cases', () => {
    it('handles very large order values', async () => {
      const largeOrderData = {
        ...mockOrderData,
        items: [
          {
            productId: 'expensive-product',
            quantity: 1,
            price: 10000,
            size: '9',
            color: 'Gold'
          }
        ],
        subtotal: 10000,
        total: 10090
      }
      
      const result = await validateOrderData(largeOrderData)
      
      expect(result.correctedTotal).toBe(10090)
      expect(typeof result.correctedTotal).toBe('number')
    })

    it('handles decimal precision correctly', async () => {
      const decimalOrderData = {
        ...mockOrderData,
        items: [
          {
            productId: 'product-1',
            quantity: 3,
            price: 33.33,
            size: '9',
            color: 'Black'
          }
        ],
        subtotal: 99.99,
        total: 189.99
      }
      
      const result = await validateOrderData(decimalOrderData)
      
      // Should handle decimal precision without floating point errors
      expect(Number.isFinite(result.correctedTotal)).toBe(true)
    })

    it('handles missing optional fields gracefully', async () => {
      const minimalOrderData = {
        customerId: 'customer-123',
        items: [
          {
            productId: 'product-1',
            quantity: 1,
            price: 100
          }
        ],
        subtotal: 100,
        total: 190
      }
      
      const result = await validateOrderData(minimalOrderData)
      
      expect(result).toBeDefined()
      expect(typeof result.isValid).toBe('boolean')
    })
  })
})
