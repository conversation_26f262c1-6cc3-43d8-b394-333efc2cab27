import { NextRequest, NextResponse } from 'next/server';
import { detectMultiColorVariants, getProductDescriptionFromImage } from '@/utils/replicate';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, category } = await request.json();

    if (!imageUrl) {
      return NextResponse.json({
        error: 'Image URL is required'
      }, { status: 400 });
    }

    // First, detect if there are multiple color variants
    const variantDetection = await detectMultiColorVariants(imageUrl);
    
    if (!variantDetection.hasMultipleVariants) {
      // Single product - return standard analysis
      const standardAnalysis = await getProductDescriptionFromImage(imageUrl, category);
      
      return NextResponse.json({
        hasMultipleVariants: false,
        singleProduct: standardAnalysis ? JSON.parse(standardAnalysis) : null
      });
    }

    // Multiple variants detected - analyze each variant
    const variants = [];
    
    for (const variant of variantDetection.variants) {
      try {
        // Create a focused prompt for this specific variant
        const variantAnalysis = await analyzeSpecificVariant(imageUrl, variant, variantDetection, category);
        if (variantAnalysis) {
          variants.push(variantAnalysis);
        }
      } catch (error) {
        console.error(`Error analyzing variant ${variant.color}:`, error);
      }
    }

    return NextResponse.json({
      hasMultipleVariants: true,
      baseProduct: {
        type: variantDetection.productType,
        model: variantDetection.baseModel,
        brand: variantDetection.brand
      },
      variants,
      originalDetection: variantDetection
    });

  } catch (error) {
    console.error('Variant detection API error:', error);
    return NextResponse.json({
      error: 'Failed to detect variants',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Analyze a specific color variant within a multi-variant image
 */
async function analyzeSpecificVariant(
  imageUrl: string, 
  variant: any, 
  baseDetection: any, 
  category?: string
): Promise<any> {
  try {
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Fetch and process image
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const base64Data = Buffer.from(arrayBuffer).toString('base64');
    const mimeType = response.headers.get('content-type') || 'image/jpeg';

    const prompt = [
      `You are analyzing a specific color variant of a ${baseDetection.productType} in this image.`,
      "",
      `FOCUS ON: The ${variant.color} variant located at ${variant.position} in the image.`,
      `BASE PRODUCT: ${baseDetection.brand} ${baseDetection.baseModel}`,
      "",
      "Create a complete product listing for this specific color variant:",
      "",
      "Return JSON with:",
      "- name: Product name including color (e.g., 'Nike Air Force 1 - Triple White')",
      "- brand: Brand name",
      "- model: Model name",
      "- type: Product type",
      "- category: Product category",
      "- main_color: Primary color of this variant",
      "- secondary_color: Secondary color if applicable, else null",
      "- description: Compelling 4-6 sentence description for this color variant",
      "- style_keywords: Array of style terms",
      "- tags: Array of product tags including color",
      "- gender: 'Men's', 'Women's', or 'Unisex'",
      "- sizes: Array of appropriate sizes based on gender",
      "- retail_price_estimate: Affordable price in Maloti (M)",
      "- stock: Random number between 15-45",
      "",
      "PRICING STRATEGY: Price 15-25% below typical market prices for affordability",
      "SIZING RULES: Women's shoes = sizes 3-7, Men's shoes = sizes 5-12, Unisex = all sizes 3-12",
      "",
      "Return only valid JSON."
    ];

    const result = await model.generateContent([
      { text: prompt.join("\n") },
      {
        inlineData: {
          data: base64Data,
          mimeType
        }
      }
    ]);

    const response_text = await result.response.text();
    
    // Extract JSON
    let jsonStart = response_text.indexOf('{');
    let jsonEnd = response_text.lastIndexOf('}');
    let jsonString = (jsonStart !== -1 && jsonEnd !== -1) ? 
      response_text.substring(jsonStart, jsonEnd + 1) : response_text;
    
    const parsed = JSON.parse(jsonString);
    
    // Add variant-specific metadata
    parsed.variantInfo = {
      originalPosition: variant.position,
      detectionConfidence: variant.confidence,
      isVariant: true,
      baseModel: baseDetection.baseModel
    };

    return parsed;

  } catch (error) {
    console.error('Error analyzing specific variant:', error);
    return null;
  }
}
