import { NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// GET /api/admin/products/category-issues - Find products that might be in wrong categories
export async function GET(request: NextRequest) {
  try {
    await requireAdmin();

    // Get all products with their categories
    const products = await prisma.product.findMany({
      include: {
        category: true,
      },
      orderBy: { name: "asc" },
    });

    // Define category keywords for detection
    const categoryKeywords = {
      'Accessories': ['bag', 'handbag', 'purse', 'backpack', 'tote', 'clutch', 'satchel', 'messenger', 'duffle', 'briefcase', 'wallet', 'pouch', 'watch', 'belt', 'jewelry', 'necklace', 'bracelet', 'earring'],
      'Sneakers': ['sneaker', 'shoe', 'boot', 'sandal', 'slipper', 'loafer', 'trainer', 'runner', 'basketball', 'tennis', 'athletic', 'footwear', 'jordan', 'nike', 'adidas'],
      'Fashionwear': ['shirt', 'dress', 'pants', 'jeans', 'jacket', 'coat', 'sweater', 'hoodie', 'top', 'blouse', 'skirt', 'shorts'],
      'Headwear': ['cap', 'hat', 'beanie', 'helmet', 'headband', 'visor'],
      'Intimates': ['underwear', 'bra', 'panties', 'lingerie', 'boxers', 'briefs', 'undershirt']
    };

    // Find potential mismatches
    const potentialIssues = products.filter(product => {
      const productName = product.name.toLowerCase();
      const productDescription = (product.description || '').toLowerCase();
      const currentCategory = product.category.name;

      // Check if product contains keywords for a different category
      for (const [categoryName, keywords] of Object.entries(categoryKeywords)) {
        if (categoryName !== currentCategory) {
          const hasKeyword = keywords.some(keyword => 
            productName.includes(keyword) || productDescription.includes(keyword)
          );
          
          if (hasKeyword) {
            return true;
          }
        }
      }
      
      return false;
    });

    // Categorize the issues
    const categorizedIssues = potentialIssues.map(product => {
      const productName = product.name.toLowerCase();
      const productDescription = (product.description || '').toLowerCase();
      const currentCategory = product.category.name;
      
      let suggestedCategory = currentCategory;
      let confidence = 0;
      
      // Find the best matching category
      for (const [categoryName, keywords] of Object.entries(categoryKeywords)) {
        const matchCount = keywords.filter(keyword => 
          productName.includes(keyword) || productDescription.includes(keyword)
        ).length;
        
        if (matchCount > confidence) {
          confidence = matchCount;
          suggestedCategory = categoryName;
        }
      }
      
      return {
        id: product.id,
        name: product.name,
        currentCategory: currentCategory,
        currentCategoryId: product.categoryId,
        suggestedCategory,
        confidence,
        brand: product.brand,
        price: product.price,
        images: product.images.slice(0, 1), // Just first image
      };
    });

    // Sort by confidence (highest first)
    categorizedIssues.sort((a, b) => b.confidence - a.confidence);

    const response: ApiResponse<typeof categorizedIssues> = {
      success: true,
      data: categorizedIssues,
      message: `Found ${categorizedIssues.length} potential category issues`,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error finding category issues:", error);
    return NextResponse.json(
      { success: false, error: "Failed to find category issues" },
      { status: 500 }
    );
  }
}
