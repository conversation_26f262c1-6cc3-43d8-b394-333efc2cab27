"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useSession } from '@/lib/auth-client';
import { Progress } from '@/components/ui/progress';
import { Facebook, Instagram } from 'lucide-react';
import { PartnerQRCode } from '@/components/ui/qr-code';

export default function PartnerDashboardPage() {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const [partner, setPartner] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isPending) return;
    if (!session?.user) {
      router.replace('/profile');
      return;
    }
    // Fetch partner data
    fetch('/api/partner/dashboard')
      .then(res => res.json())
      .then(data => {
        if (!data.partner) {
          router.replace('/profile');
          return;
        }
        setPartner(data.partner)
      })
      .catch(() => setError('Failed to load dashboard'))
      .finally(() => setLoading(false));
  }, [session, isPending, router]);

  if (isPending || loading) return <div className="p-8 text-center">Loading...</div>;
  if (error) return <div className="p-8 text-center text-red-600">{error}</div>;
  if (!partner) return <div className="p-8 text-center">Not a partner.</div>;

  // Calculate progress and monthly stats
  const totalReferrals = partner.referralOrders?.length || 0;
  const progress = Math.min((totalReferrals / 10) * 100, 100);
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  const monthlyOrders = (partner.referralOrders || []).filter((order: any) => {
    const d = new Date(order.createdAt);
    return d.getMonth() === currentMonth && d.getFullYear() === currentYear;
  });
  const monthlyCommission = monthlyOrders.reduce((sum: number, o: any) => sum + o.commission, 0);

  // Performance tip
  let performanceTip = '';
  if (totalReferrals < 10) {
    const toGo = 10 - totalReferrals;
    performanceTip = `You’re only ${toGo} referral${toGo === 1 ? '' : 's'} away from your M300 bonus! Share your code to reach the milestone.`;
  } else {
    performanceTip = 'You’ve unlocked your bonus! Keep referring to earn even more.';
  }

  // Best month ever
  const monthStats: Record<string, { count: number; commission: number }> = {};
  (partner.referralOrders || []).forEach((order: any) => {
    const d = new Date(order.createdAt);
    const key = `${d.getFullYear()}-${d.getMonth()}`;
    if (!monthStats[key]) monthStats[key] = { count: 0, commission: 0 };
    monthStats[key].count++;
    monthStats[key].commission += order.commission;
  });
  let bestMonth = null;
  let bestMonthKey = '';
  for (const key in monthStats) {
    if (!bestMonth || monthStats[key].count > bestMonth.count) {
      bestMonth = monthStats[key];
      bestMonthKey = key;
    }
  }
  let bestMonthLabel = '';
  if (bestMonth) {
    const [year, month] = bestMonthKey.split('-');
    const date = new Date(Number(year), Number(month));
    bestMonthLabel = `${date.toLocaleString('default', { month: 'long' })} ${year}`;
  }

  // Estimated next bonus
  let estimatedBonus = '';
  if (totalReferrals < 10 && monthlyOrders.length > 0) {
    const avgPerMonth = (partner.referralOrders.length) / ((now.getFullYear() - new Date(partner.createdAt).getFullYear()) * 12 + (now.getMonth() - new Date(partner.createdAt).getMonth()) + 1);
    const toGo = 10 - totalReferrals;
    const estMonths = Math.ceil(toGo / (avgPerMonth || 1));
    const estDate = new Date(now.getFullYear(), now.getMonth() + estMonths, 1);
    estimatedBonus = `At your current pace, you’ll reach your bonus around ${estDate.toLocaleString('default', { month: 'long', year: 'numeric' })}.`;
  }

  // Share links
  const referralUrl = `https://rivvsneakers.shop/?ref=${partner.referralCode}&discount=${partner.discountCode}`;
  const fbShare = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralUrl)}`;
  const igShare = `https://www.instagram.com/?url=${encodeURIComponent(referralUrl)}`;

  return (
    <div className="max-w-4xl mx-auto py-4 px-3 sm:py-8 sm:px-4 lg:px-0">
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-xl sm:text-2xl">My Referral Dashboard</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Referral Code and Commission Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="font-semibold text-base">Your Referral Code:</div>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <code className="bg-gray-100 px-3 py-2 rounded text-base sm:text-lg font-mono break-all">{partner.referralCode}</code>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => navigator.clipboard.writeText(partner.referralCode)}
                  className="w-full sm:w-auto"
                >
                  Copy
                </Button>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-gray-600">
                  Discount Code: <code className="bg-green-100 px-2 py-1 rounded font-mono">{partner.discountCode}</code>
                </div>
                <div className="text-sm text-gray-600">
                  Discount Amount: <strong>M{partner.discountAmount?.toFixed(2) || '0.00'}</strong>
                </div>
              </div>
            </div>
            <div className="text-center lg:text-right bg-green-50 p-4 rounded-lg">
              <div className="font-semibold text-base mb-2">Commission Earned</div>
              <div className="text-2xl sm:text-3xl text-green-700 font-bold">M{partner.commissionEarned?.toFixed(2) || '0.00'}</div>
              <div className="text-sm text-gray-500 mt-1">Bonus Paid: M{partner.bonusPaid?.toFixed(2) || '0.00'}</div>
            </div>
          </div>

          {/* QR Code Section */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h4 className="font-semibold text-blue-800 mb-4 text-center">Your QR Code</h4>
            <div className="flex justify-center">
              <PartnerQRCode
                referralCode={partner.referralCode}
                discountCode={partner.discountCode}
                discountAmount={partner.discountAmount || 0}
                partnerName={`${partner.name} ${partner.surname}`}
                size={200}
                showDownload={true}
                showRefresh={true}
              />
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-3">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
              <span className="font-semibold text-sm sm:text-base">Progress toward 10-referral bonus (M300)</span>
              <span className="text-sm text-gray-600 font-medium">{totalReferrals} / 10</span>
            </div>
            <Progress value={progress} className="h-4" />
            {progress === 100 && (
              <div className="text-green-700 font-semibold mt-2">Congratulations! You’ve reached the bonus milestone.</div>
            )}
          </div>

          {/* Monthly Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
              <div className="text-2xl sm:text-3xl font-bold text-blue-700">{monthlyOrders.length}</div>
              <div className="text-xs sm:text-sm text-gray-700 mt-1">Referrals this month</div>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <div className="text-2xl sm:text-3xl font-bold text-green-700">M{monthlyCommission.toFixed(2)}</div>
              <div className="text-xs sm:text-sm text-gray-700 mt-1">Commission this month</div>
            </div>
          </div>

          {/* Admin Notes */}
          {partner.adminNotes && (
            <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="font-semibold mb-1">Message from Katleho Namane</div>
              <div className="text-gray-800">{partner.adminNotes}</div>
            </div>
          )}

          {/* Performance Tips */}
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="font-semibold mb-1">Referral Performance Tips</div>
            <div className="text-blue-800">{performanceTip}</div>
            {estimatedBonus && <div className="text-sm text-blue-600 mt-1">{estimatedBonus}</div>}
          </div>

          {/* Best Month Ever */}
          {bestMonth && (
            <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="font-semibold mb-1">Best Month Ever</div>
              <div className="text-green-800">{bestMonthLabel}: {bestMonth.count} referrals, M{bestMonth.commission.toFixed(2)} commission</div>
            </div>
          )}

          {/* Quick Share */}
          <div className="space-y-3">
            <span className="font-semibold text-base">Share your referral link:</span>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                size="sm"
                variant="outline"
                onClick={() => window.open(fbShare, '_blank')}
                className="w-full sm:w-auto justify-center"
              >
                <Facebook className="h-4 w-4 mr-2" /> Facebook
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => window.open(igShare, '_blank')}
                className="w-full sm:w-auto justify-center"
              >
                <Instagram className="h-4 w-4 mr-2" /> Instagram
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <div className="font-semibold text-base">Referred Orders</div>
            {partner.referralOrders && partner.referralOrders.length > 0 ? (
              <div className="space-y-3">
                {/* Desktop Table */}
                <div className="hidden sm:block overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-3">Customer</th>
                        <th className="text-left py-3 px-3">Order Value</th>
                        <th className="text-left py-3 px-3">Commission</th>
                        <th className="text-left py-3 px-3">Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {partner.referralOrders.map((order: any) => (
                        <tr key={order.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-3 font-medium">{order.customerName}</td>
                          <td className="py-3 px-3">M{order.orderValue.toFixed(2)}</td>
                          <td className="py-3 px-3 text-green-600 font-medium">M{order.commission.toFixed(2)}</td>
                          <td className="py-3 px-3 text-gray-600">{new Date(order.createdAt).toLocaleDateString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Mobile Cards */}
                <div className="sm:hidden space-y-3">
                  {partner.referralOrders.map((order: any) => (
                    <div key={order.id} className="bg-gray-50 rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-start">
                        <div className="font-medium text-gray-900">{order.customerName}</div>
                        <div className="text-sm text-gray-500">{new Date(order.createdAt).toLocaleDateString()}</div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600">Order Value: <span className="font-medium">M{order.orderValue.toFixed(2)}</span></div>
                        <div className="text-sm text-green-600 font-medium">Commission: M{order.commission.toFixed(2)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-center py-8 bg-gray-50 rounded-lg">
                No referred orders yet. Start sharing your referral code to earn commissions!
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 