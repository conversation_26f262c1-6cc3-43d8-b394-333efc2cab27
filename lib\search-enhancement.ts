/**
 * Search Enhancement System
 * Generates tags, keywords, and search terms for products
 */

// Common sneaker/fashion terms and their variations
const BRAND_VARIATIONS: Record<string, string[]> = {
  'nike': ['nike', 'air', 'jordan', 'air jordan', 'aj', 'swoosh'],
  'adidas': ['adidas', 'three stripes', '3 stripes', 'boost', 'ultraboost'],
  'puma': ['puma', 'cat', 'suede'],
  'vans': ['vans', 'off the wall', 'sk8', 'old skool'],
  'converse': ['converse', 'chuck taylor', 'all star', 'chuck'],
  'new balance': ['new balance', 'nb', 'made in usa'],
  'reebok': ['reebok', 'classic'],
  'lacoste': ['lacoste', 'crocodile', 'polo'],
  'calvin klein': ['calvin klein', 'ck', 'underwear', 'intimates']
};

const PRODUCT_TYPE_KEYWORDS: Record<string, string[]> = {
  'sneakers': ['sneakers', 'shoes', 'trainers', 'kicks', 'footwear', 'athletic shoes', 'sports shoes'],
  'basketball': ['basketball', 'hoops', 'court', 'high top', 'performance'],
  'running': ['running', 'jogging', 'marathon', 'fitness', 'cardio'],
  'casual': ['casual', 'lifestyle', 'everyday', 'street', 'urban'],
  'retro': ['retro', 'vintage', 'classic', 'throwback', 'old school'],
  'high': ['high top', 'high', 'ankle', 'boot'],
  'low': ['low top', 'low', 'slip on'],
  'mid': ['mid top', 'mid', 'medium'],
  'jacket': ['jacket', 'outerwear', 'coat', 'blazer'],
  'watch': ['watch', 'timepiece', 'accessory', 'wrist'],
  'cap': ['cap', 'hat', 'headwear', 'baseball cap'],
  'shorts': ['shorts', 'underwear', 'intimates', 'boxer']
};

const COLOR_KEYWORDS: Record<string, string[]> = {
  'black': ['black', 'dark', 'noir', 'midnight'],
  'white': ['white', 'cream', 'off-white', 'ivory', 'pearl'],
  'red': ['red', 'crimson', 'cherry', 'burgundy'],
  'blue': ['blue', 'navy', 'royal', 'sky', 'denim'],
  'green': ['green', 'forest', 'olive', 'mint'],
  'yellow': ['yellow', 'gold', 'golden', 'amber'],
  'gray': ['gray', 'grey', 'silver', 'charcoal'],
  'brown': ['brown', 'tan', 'chocolate', 'coffee'],
  'pink': ['pink', 'rose', 'blush'],
  'purple': ['purple', 'violet', 'lavender']
};

const SIZE_KEYWORDS: Record<string, string[]> = {
  'small': ['s', 'small', 'xs', 'extra small'],
  'medium': ['m', 'medium', 'med'],
  'large': ['l', 'large', 'xl', 'extra large'],
  'numeric': ['size', 'us', 'uk', 'eu']
};

/**
 * Extract keywords from product name using pattern matching
 */
export function extractKeywordsFromName(name: string): string[] {
  const keywords: Set<string> = new Set();
  const nameLower = name.toLowerCase();
  
  // Add the full name as a search term
  keywords.add(name.toLowerCase());
  
  // Split name into words and add each word
  const words = nameLower.split(/[\s\-_]+/).filter(word => word.length > 1);
  words.forEach(word => keywords.add(word));
  
  // Check for brand variations
  Object.entries(BRAND_VARIATIONS).forEach(([brand, variations]) => {
    if (variations.some(variation => nameLower.includes(variation))) {
      keywords.add(brand);
      variations.forEach(variation => keywords.add(variation));
    }
  });
  
  // Check for product type keywords
  Object.entries(PRODUCT_TYPE_KEYWORDS).forEach(([type, typeKeywords]) => {
    if (typeKeywords.some(keyword => nameLower.includes(keyword))) {
      keywords.add(type);
      typeKeywords.forEach(keyword => keywords.add(keyword));
    }
  });
  
  // Extract numbers (for model numbers, years, etc.)
  const numbers = nameLower.match(/\d+/g);
  if (numbers) {
    numbers.forEach(num => keywords.add(num));
  }
  
  return Array.from(keywords);
}

/**
 * Generate tags from product attributes
 */
export function generateProductTags(product: {
  name: string;
  brand: string;
  category?: { name: string };
  colors?: string[];
  sizes?: string[];
  description?: string;
}): string[] {
  const tags: Set<string> = new Set();
  
  // Brand tag
  tags.add(product.brand.toLowerCase());
  
  // Category tag
  if (product.category) {
    tags.add(product.category.name.toLowerCase());
  }
  
  // Color tags
  if (product.colors) {
    product.colors.forEach(color => {
      const colorLower = color.toLowerCase();
      tags.add(colorLower);
      
      // Add color variations
      Object.entries(COLOR_KEYWORDS).forEach(([baseColor, variations]) => {
        if (variations.some(variation => colorLower.includes(variation))) {
          tags.add(baseColor);
        }
      });
    });
  }
  
  // Size-based tags
  if (product.sizes) {
    const hasNumericSizes = product.sizes.some(size => /\d/.test(size));
    const hasLetterSizes = product.sizes.some(size => /^[a-zA-Z]+$/.test(size));
    
    if (hasNumericSizes) tags.add('numeric-sizes');
    if (hasLetterSizes) tags.add('letter-sizes');
    
    // Add specific size tags
    product.sizes.forEach(size => {
      const sizeLower = size.toLowerCase();
      Object.entries(SIZE_KEYWORDS).forEach(([sizeType, sizeKeywords]) => {
        if (sizeKeywords.some(keyword => sizeLower.includes(keyword))) {
          tags.add(sizeType);
        }
      });
    });
  }
  
  // Product type tags from name
  const nameLower = product.name.toLowerCase();
  Object.keys(PRODUCT_TYPE_KEYWORDS).forEach(type => {
    if (PRODUCT_TYPE_KEYWORDS[type].some(keyword => nameLower.includes(keyword))) {
      tags.add(type);
    }
  });
  
  // Special tags for popular models
  if (nameLower.includes('jordan')) {
    tags.add('air-jordan');
    tags.add('basketball');
    tags.add('retro');
  }
  
  if (nameLower.includes('ultraboost') || nameLower.includes('boost')) {
    tags.add('running');
    tags.add('performance');
  }
  
  if (nameLower.includes('retro') || nameLower.match(/\d{1,2}$/)) {
    tags.add('retro');
    tags.add('classic');
  }
  
  return Array.from(tags);
}

/**
 * Generate comprehensive search terms
 */
export function generateSearchTerms(product: {
  name: string;
  brand: string;
  category?: { name: string };
  colors?: string[];
  sizes?: string[];
  description?: string;
}): string[] {
  const searchTerms: Set<string> = new Set();
  
  // Add keywords from name
  const nameKeywords = extractKeywordsFromName(product.name);
  nameKeywords.forEach(keyword => searchTerms.add(keyword));
  
  // Add brand variations
  const brandLower = product.brand.toLowerCase();
  if (BRAND_VARIATIONS[brandLower]) {
    BRAND_VARIATIONS[brandLower].forEach(variation => searchTerms.add(variation));
  }
  
  // Add category terms
  if (product.category) {
    searchTerms.add(product.category.name.toLowerCase());
  }
  
  // Add description keywords
  if (product.description) {
    const descWords = product.description.toLowerCase()
      .split(/[\s\-_.,!?]+/)
      .filter(word => word.length > 2);
    descWords.forEach(word => searchTerms.add(word));
  }
  
  // Add color search terms
  if (product.colors) {
    product.colors.forEach(color => {
      const colorLower = color.toLowerCase();
      searchTerms.add(colorLower);
      
      // Add color variations
      Object.entries(COLOR_KEYWORDS).forEach(([baseColor, variations]) => {
        if (variations.some(variation => colorLower.includes(variation))) {
          variations.forEach(variation => searchTerms.add(variation));
        }
      });
    });
  }
  
  return Array.from(searchTerms);
}

/**
 * Calculate search relevance score
 */
export function calculateSearchScore(product: {
  name: string;
  brand: string;
  rating?: number;
  reviewCount?: number;
  stock?: number;
  isActive?: boolean;
}): number {
  let score = 0;
  
  // Base score from rating
  if (product.rating) {
    score += product.rating * 20; // Max 100 points
  }
  
  // Review count bonus
  if (product.reviewCount) {
    score += Math.min(product.reviewCount * 0.5, 50); // Max 50 points
  }
  
  // Stock availability
  if (product.stock && product.stock > 0) {
    score += 30; // 30 points for being in stock
  }
  
  // Active product bonus
  if (product.isActive) {
    score += 20; // 20 points for being active
  }
  
  // Brand popularity (based on your preferences)
  const brandLower = product.brand.toLowerCase();
  const brandScores: Record<string, number> = {
    'nike': 50,
    'adidas': 40,
    'lacoste': 30,
    'vans': 25,
    'puma': 20,
    'converse': 15,
    'new balance': 15,
    'reebok': 10
  };
  
  if (brandScores[brandLower]) {
    score += brandScores[brandLower];
  }
  
  return Math.min(score, 300); // Cap at 300
}
