"use client";

import { useCart } from "@/contexts/cart-context";
import { useReferral } from "@/contexts/referral-context";
import { getEffectivePriceWithReferral } from "@/lib/product-utils";
import { useMemo } from "react";

export function useCartWithReferral() {
  const { state: cartState, ...cartActions } = useCart();
  const { referralDiscount } = useReferral();

  const cartTotalsWithReferral = useMemo(() => {
    const totalItems = cartState.totalItems;
    
    // Calculate total price with referral discounts applied
    const totalPrice = cartState.items.reduce((sum, item) => {
      const effectivePrice = getEffectivePriceWithReferral(item.product, referralDiscount);
      return sum + (effectivePrice * item.quantity);
    }, 0);

    // Calculate total referral savings
    const totalReferralSavings = cartState.items.reduce((sum, item) => {
      const basePrice = item.product.discountedPrice || item.product.price;
      const effectivePrice = getEffectivePriceWithReferral(item.product, referralDiscount);
      const savings = basePrice - effectivePrice;
      return sum + (savings * item.quantity);
    }, 0);

    return {
      totalItems,
      totalPrice,
      totalReferralSavings,
      originalTotalPrice: cartState.totalPrice
    };
  }, [cartState.items, cartState.totalItems, cartState.totalPrice, referralDiscount]);

  return {
    state: {
      ...cartState,
      ...cartTotalsWithReferral
    },
    ...cartActions
  };
}
