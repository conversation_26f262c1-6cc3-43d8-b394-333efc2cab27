import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UploadDropzone } from "@/lib/uploadthing";
import { X, Upload, Package } from "lucide-react";
import { Switch } from "@/components/ui/switch";

export interface Category {
  id: string;
  name: string;
  description?: string;
}

interface ProductFormProps {
  initialProduct?: any;
  categories: Category[];
  onSubmit: (data: any) => void;
  loading: boolean;
  error: string;
  mode: "create" | "edit";
}

// All possible sizes for different product types
const allSizes = {
  shoes: ["3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
  clothing: ["XS", "S", "M", "L", "XL", "XXL"],
  accessories: ["One Size", "XS", "S", "M", "L", "XL"],
  headwear: ["S", "M", "L", "XL"],
  general: ["XS", "S", "M", "L", "XL"]
};

// Combined default sizes for the UI (shows all possible options)
const defaultSizes = [
  ...allSizes.shoes,
  ...allSizes.clothing,
  ...allSizes.accessories,
  ...allSizes.headwear
].filter((size, index, arr) => arr.indexOf(size) === index); // Remove duplicates

const defaultColors = ["Black", "White", "Red", "Blue", "Green", "Yellow", "Orange", "Purple", "Pink", "Brown", "Gray", "Navy", "Beige", "Maroon", "Olive"];

export default function ProductForm({
  initialProduct,
  categories,
  onSubmit,
  loading,
  error,
  mode,
}: ProductFormProps) {
  const [form, setForm] = useState({
    name: initialProduct?.name || "",
    description: initialProduct?.description || "",
    price: initialProduct?.price?.toString() || "",
    discountedPrice: initialProduct?.discountedPrice?.toString() || "",
    brand: initialProduct?.brand || "",
    categoryId: initialProduct?.categoryId || "",
    stock: initialProduct?.stock?.toString() || "",
  });
  const [selectedSizes, setSelectedSizes] = useState<string[]>(initialProduct?.sizes || []);
  const [selectedColors, setSelectedColors] = useState<string[]>(initialProduct?.colors || []);
  const [images, setImages] = useState<string[]>(initialProduct?.images || []);
  const [detectedGender, setDetectedGender] = useState<string>("");
  const [lastUploadResponse, setLastUploadResponse] = useState<any>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [feeForm, setFeeForm] = useState({
    costPrice: initialProduct?.costPrice?.toString() || "",
    shippingFee: initialProduct?.shippingFee?.toString() || "",
    lateCollectionFee: initialProduct?.lateCollectionFee ? true : false,
  });
  const [defaultFees, setDefaultFees] = useState({
    costPrice: "",
    shippingFee: "",
    lateCollectionFee: false,
  });

  useEffect(() => {
    if (initialProduct) {
      setForm({
        name: initialProduct.name || "",
        description: initialProduct.description || "",
        price: initialProduct.price?.toString() || "",
        discountedPrice: initialProduct.discountedPrice?.toString() || "",
        brand: initialProduct.brand || "",
        categoryId: initialProduct.categoryId || "",
        stock: initialProduct.stock?.toString() || "",
      });
      setSelectedSizes(initialProduct.sizes || []);
      setSelectedColors(initialProduct.colors || []);
      setImages(initialProduct.images || []);
    }
  }, [initialProduct]);

  useEffect(() => {
    if (!initialProduct) {
      // Fetch default fees from settings API
      fetch("/api/admin/settings").then(res => res.json()).then(result => {
        if (result.success && result.data) {
          setFeeForm({
            costPrice: "",
            shippingFee: result.data.defaultShippingFee?.toString() || "100",
            lateCollectionFee: false,
          });
          setDefaultFees({
            costPrice: "",
            shippingFee: result.data.defaultShippingFee?.toString() || "100",
            lateCollectionFee: false,
          });
        }
      });
    }
  }, [initialProduct]);

  const detectCategoryAndSetSizes = (productName: string, categoryId?: string) => {
    const name = productName.toLowerCase();
    const currentCategoryId = categoryId || form.categoryId;
    const category = categories.find(c => c.id === currentCategoryId);
    const categoryName = category?.name.toLowerCase() || "";

    let defaultSizes: string[] = [];
    let gender = "";

    // Determine size type based on category and product name
    if (categoryName.includes("sneaker") || categoryName.includes("footwear") ||
        name.includes("shoe") || name.includes("sneaker") || name.includes("boot") ||
        name.includes("sandal") || name.includes("slipper")) {

      // Shoe sizes - detect gender for appropriate range
      const womenKeywords = ["women", "womens", "woman", "ladies", "female", "girl", "girls"];
      const menKeywords = ["men", "mens", "man", "male", "boy", "boys"];

      if (womenKeywords.some(keyword => name.includes(keyword))) {
        gender = "women";
        defaultSizes = ["3", "4", "5", "6", "7"]; // Women's shoe sizes
      } else if (menKeywords.some(keyword => name.includes(keyword))) {
        gender = "men";
        defaultSizes = ["5", "6", "7", "8", "9", "10", "11", "12"]; // Men's shoe sizes
      } else {
        gender = "unisex";
        defaultSizes = ["3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]; // Unisex shoe sizes
      }
    } else if (categoryName.includes("fashionwear") || categoryName.includes("clothing") ||
               name.includes("shirt") || name.includes("dress") || name.includes("pants") ||
               name.includes("jacket") || name.includes("hoodie") || name.includes("top") ||
               name.includes("blouse") || name.includes("skirt") || name.includes("shorts") ||
               name.includes("sweater") || name.includes("coat")) {

      // Clothing sizes
      defaultSizes = ["XS", "S", "M", "L", "XL", "XXL"];
      gender = "clothing";
    } else if (categoryName.includes("accessories") || categoryName.includes("accessory") ||
               name.includes("bag") || name.includes("handbag") || name.includes("backpack") ||
               name.includes("purse") || name.includes("wallet") || name.includes("belt") ||
               name.includes("watch") || name.includes("jewelry")) {

      // Accessories - most don't need sizes, but some do
      if (name.includes("belt") || name.includes("ring") || name.includes("bracelet")) {
        defaultSizes = ["XS", "S", "M", "L", "XL"]; // Size-dependent accessories
      } else {
        defaultSizes = ["One Size"]; // Most accessories are one size
      }
      gender = "accessory";
    } else if (categoryName.includes("headwear") ||
               name.includes("cap") || name.includes("hat") || name.includes("beanie")) {

      // Headwear sizes
      defaultSizes = ["S", "M", "L", "XL"];
      gender = "headwear";
    } else if (categoryName.includes("intimate") ||
               name.includes("underwear") || name.includes("bra") || name.includes("panties") ||
               name.includes("boxers") || name.includes("briefs")) {

      // Intimate apparel sizes
      defaultSizes = ["XS", "S", "M", "L", "XL", "XXL"];
      gender = "intimate";
    } else {
      // Default fallback - assume clothing sizes
      defaultSizes = ["XS", "S", "M", "L", "XL"];
      gender = "general";
    }

    setDetectedGender(gender);

    // Only auto-set sizes if no sizes are currently selected (for new products)
    if (selectedSizes.length === 0) {
      setSelectedSizes(defaultSizes);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });

    // Auto-detect category and set sizes when product name changes
    if (name === "name" && value.trim()) {
      detectCategoryAndSetSizes(value);
    }
  };

  const handleImageUpload = (uploadedFiles: any[]) => {
    if (!uploadedFiles || !Array.isArray(uploadedFiles)) {
      console.error("Invalid uploaded files:", uploadedFiles);
      return;
    }
    const newImageUrls = uploadedFiles
      .filter(file => file && file.url)
      .map(file => file.url);

    if (newImageUrls.length > 0) {
      const currentImageCount = images.length;
      const totalAfterUpload = currentImageCount + newImageUrls.length;

      if (totalAfterUpload > 10) {
        const allowedCount = 10 - currentImageCount;
        if (allowedCount > 0) {
          const allowedImages = newImageUrls.slice(0, allowedCount);
          setImages(prev => [...prev, ...allowedImages]);
          alert(`Only ${allowedCount} image(s) were added. Maximum 10 images allowed per product. You now have ${currentImageCount + allowedCount} images.`);
        } else {
          alert(`Cannot add more images. Maximum 10 images allowed per product. You already have ${currentImageCount} images.`);
        }
      } else {
        setImages(prev => [...prev, ...newImageUrls]);
        alert(`Successfully uploaded ${newImageUrls.length} image(s)! You can see them above. Total: ${totalAfterUpload}/10 images.`);
      }
    }
    setIsUploading(false);
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFeeForm({ ...feeForm, [e.target.name]: e.target.value });
  };
  const handleLateFeeToggle = (checked: boolean) => {
    setFeeForm({ ...feeForm, lateCollectionFee: checked });
  };

  const handleAiGenerate = async () => {
    if (images.length === 0) {
      alert("Please upload an image first to generate AI description.");
      return;
    }
    try {
      const res = await fetch("/api/admin/analyze-product", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageUrl: images[0] }),
      });
      const result = await res.json();
      if (result.description) {
        const aiData = JSON.parse(result.description);
        setForm(prev => ({
          ...prev,
          name: aiData.name || prev.name,
          description: aiData.description || prev.description,
          brand: aiData.brand || prev.brand,
        }));
      } else {
        alert("AI could not generate a description. Please try another image.");
      }
    } catch (error) {
      alert("Failed to generate AI description.");
    }
  };

  const validateForm = () => {
    if (!form.name.trim()) return "Product name is required";
    if (!form.price || parseFloat(form.price) <= 0) return "Valid price is required";
    if (!form.brand.trim()) return "Brand is required";
    if (!form.categoryId) return "Category is required";
    if (selectedSizes.length === 0) return "At least one size must be selected";
    if (images.length === 0) return "At least one image is required";
    if (images.length > 10) return "Maximum 10 images allowed per product";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }
    onSubmit({
      ...form,
      price: parseFloat(form.price),
      discountedPrice: form.discountedPrice ? parseFloat(form.discountedPrice) : undefined,
      images,
      sizes: selectedSizes,
      colors: selectedColors,
      stock: form.stock ? parseInt(form.stock) : 0,
      costPrice: feeForm.costPrice ? parseFloat(feeForm.costPrice) : undefined,
      shippingFee: feeForm.shippingFee ? parseFloat(feeForm.shippingFee) : undefined,
      lateCollectionFee: feeForm.lateCollectionFee ? 10 : 0, // Use 10 as default, or fetch from settings if needed
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {mode === "edit" ? "Edit Product" : "Basic Information"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Product Name *</Label>
              <Input 
                id="name" 
                name="name" 
                value={form.name} 
                onChange={handleChange} 
                placeholder="Enter product name"
                required 
              />
            </div>
            <div>
              <Label htmlFor="brand">Brand *</Label>
              <Input 
                id="brand" 
                name="brand" 
                value={form.brand} 
                onChange={handleChange} 
                placeholder="Enter brand name"
                required 
              />
            </div>
          </div>
          <div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
              <Label htmlFor="description">Description</Label>
              <Button type="button" variant="outline" size="sm" onClick={handleAiGenerate} className="w-full sm:w-auto">
                ✨ <span className="hidden sm:inline">Generate AI Description</span><span className="sm:hidden">AI Generate</span>
              </Button>
            </div>
            <Textarea 
              id="description" 
              name="description" 
              value={form.description} 
              onChange={handleChange} 
              placeholder="Enter product description"
              rows={4}
            />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="price">Price (M) *</Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                value={form.price}
                onChange={handleChange}
                placeholder="0.00"
                required
              />
            </div>
            <div>
              <Label htmlFor="discountedPrice">Discounted Price (M)</Label>
              <Input
                id="discountedPrice"
                name="discountedPrice"
                type="number"
                step="0.01" 
                min="0"
                value={form.discountedPrice} 
                onChange={handleChange} 
                placeholder="0.00"
              />
            </div>
            <div>
              <Label htmlFor="stock">Stock Quantity</Label>
              <Input 
                id="stock" 
                name="stock" 
                type="number" 
                min="0"
                value={form.stock} 
                onChange={handleChange} 
                placeholder="0"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="categoryId">Category *</Label>
            <Select
              value={form.categoryId}
              onValueChange={(value) => {
                setForm(prev => ({ ...prev, categoryId: value }));
                // Re-detect sizes when category changes
                if (form.name.trim()) {
                  detectCategoryAndSetSizes(form.name, value);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      {/* Sizes and Colors */}
      <Card>
        <CardHeader>
          <CardTitle>Sizes & Colors</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <div className="flex items-center justify-between">
              <Label>Available Sizes *</Label>
              {detectedGender && (
                <Badge variant="outline" className="text-xs">
                  Detected: {detectedGender}
                </Badge>
              )}
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {defaultSizes.map((size) => (
                <Badge
                  key={size}
                  variant={selectedSizes.includes(size) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => setSelectedSizes(prev => prev.includes(size) ? prev.filter(s => s !== size) : [...prev, size])}
                >
                  {size}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Label>Available Colors</Label>
            <p className="text-sm text-gray-600 mb-2">Select colors that best describe this product variant</p>
            <div className="flex flex-wrap gap-2 mt-2">
              {defaultColors.map((color) => (
                <Badge
                  key={color}
                  variant={selectedColors.includes(color) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => setSelectedColors(prev => prev.includes(color) ? prev.filter(c => c !== color) : [...prev, color])}
                >
                  {color}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Cost & Fees */}
      <Card>
        <CardHeader>
          <CardTitle>Cost & Fees</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="costPrice">Cost Price (M) *</Label>
              <Input
                id="costPrice"
                name="costPrice"
                type="number"
                step="0.01"
                min="0"
                value={feeForm.costPrice}
                onChange={handleFeeChange}
                placeholder="0.00"
                required
              />
            </div>
            <div>
              <Label htmlFor="shippingFee">Shipping Fee (M)</Label>
              <Input
                id="shippingFee"
                name="shippingFee"
                type="number"
                step="0.01"
                min="0"
                value={feeForm.shippingFee}
                onChange={handleFeeChange}
                placeholder={defaultFees.shippingFee || "100"}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="lateCollectionFee">Late Collection Fee (M10)</Label>
              <div className="flex items-center gap-2">
                <Switch
                  id="lateCollectionFee"
                  checked={feeForm.lateCollectionFee}
                  onCheckedChange={handleLateFeeToggle}
                />
                <span>{feeForm.lateCollectionFee ? "Applied" : "Not Applied"}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Image Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Product Images *
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {images.length > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-700">
                  Current Images ({images.length}/7)
                </h4>
                <span className="text-xs text-gray-500">
                  Click the X to remove an image
                </span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {images.map((image, index) => (
                  <div key={index} className="relative group">
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-blue-300 transition-colors">
                      <img
                        src={image}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg"
                      title="Remove image"
                    >
                      <X className="h-4 w-4" />
                    </button>
                    <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {index + 1}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Upload className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No images uploaded yet</p>
              <p className="text-xs text-gray-400">Upload images below to see them here</p>
            </div>
          )}
          <div className="space-y-2">
            {images.length >= 10 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50">
                <div className="text-gray-500">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm font-medium">Maximum images reached</p>
                  <p className="text-xs">You have uploaded the maximum of 10 images per product</p>
                  <p className="text-xs text-gray-400 mt-1">Remove an image above to upload a new one</p>
                </div>
              </div>
            ) : (
              <UploadDropzone
                endpoint="productImageUploader"
                onClientUploadComplete={(res) => {
                  setLastUploadResponse(res);
                  if (res && Array.isArray(res)) {
                    handleImageUpload(res);
                  } else {
                    alert("Upload completed but response format was unexpected. Please ensure you are logged in as an admin and try again.");
                    setIsUploading(false);
                  }
                }}
                onUploadError={(error: Error) => {
                  alert(`Upload failed: ${error.message}. Please ensure you are logged in as an admin and try again.`);
                  setIsUploading(false);
                }}
                onUploadBegin={() => {
                  setIsUploading(true);
                }}
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  isUploading
                    ? 'border-blue-400 bg-blue-50'
                    : images.length >= 9
                    ? 'border-orange-300 hover:border-orange-400 bg-orange-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              />
            )}
            {isUploading && (
              <div className="flex items-center justify-center gap-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">Uploading images... They will appear above once uploaded.</span>
              </div>
            )}
            {images.length >= 9 && images.length < 10 && (
              <div className="text-center text-orange-600 text-xs">
                ⚠️ You can upload {10 - images.length} more image(s) (Maximum: 10)
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {/* Submit Button */}
      <div className="flex justify-end gap-4">
        <Button type="submit" disabled={loading}>
          {loading ? (mode === "edit" ? "Updating..." : "Creating Product...") : (mode === "edit" ? "Update Product" : "Create Product")}
        </Button>
      </div>
    </form>
  );
} 