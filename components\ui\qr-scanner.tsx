'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Camera, X, CheckCircle, AlertCircle, Upload, FileImage } from 'lucide-react';
import { Html5QrcodeScanner, Html5Qrcode, Html5QrcodeScanType } from 'html5-qrcode';

interface QRScannerProps {
  onScan: (data: { referralCode?: string; discountCode?: string }) => void;
  onError?: (error: string) => void;
  trigger?: React.ReactNode;
}

export default function QRScanner({ onScan, onError, trigger }: QRScannerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [scanResult, setScanResult] = useState<{ referralCode?: string; discountCode?: string } | null>(null);
  const [error, setError] = useState<string>('');
  const [scanMode, setScanMode] = useState<'camera' | 'upload'>('camera');
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const parseQRData = (qrData: string) => {
    try {
      const url = new URL(qrData);
      const referralCode = url.searchParams.get('ref') || undefined;
      const discountCode = url.searchParams.get('discount') || undefined;
      
      return { referralCode, discountCode };
    } catch (err) {
      // If it's not a URL, try to parse as just a code
      return { referralCode: qrData, discountCode: undefined };
    }
  };

  const validateQRData = (qrData: string): boolean => {
    // Check if the QR code contains valid data
    if (!qrData || qrData.trim().length === 0) {
      return false;
    }
    
    // Check if it's a valid URL with our expected parameters
    try {
      const url = new URL(qrData);
      const hasRef = url.searchParams.has('ref');
      const hasDiscount = url.searchParams.has('discount');
      
      // Valid if it has either ref or discount parameter, or if it's our domain
      return hasRef || hasDiscount || url.hostname.includes('rivvsneakers.shop');
    } catch (err) {
      // If it's not a URL, check if it's a reasonable code length
      return qrData.trim().length >= 3 && qrData.trim().length <= 50;
    }
  };

  const handleScanSuccess = (decodedText: string) => {
    console.log('QR code scanned successfully:', decodedText);
    
    // Validate the scanned QR code data
    if (!validateQRData(decodedText)) {
      console.log('Invalid QR code data detected:', decodedText);
      setError('Invalid QR code detected. Please scan a valid referral or discount code QR code.');
      setIsScanning(false);
      setIsProcessingImage(false);
      return;
    }

    const parsedData = parseQRData(decodedText);
    console.log('Parsed QR data:', parsedData);
    setScanResult(parsedData);
    setIsScanning(false);
    setIsProcessingImage(false);
    
    // Stop the scanner
    if (scannerRef.current) {
      scannerRef.current.clear();
      scannerRef.current = null;
    }
  };

  const handleScanError = (errorMessage: string) => {
    console.log('QR scan error:', errorMessage);
    
    // Handle specific error types
    if (errorMessage.includes('NotFoundException') || errorMessage.includes('No MultiFormat Readers')) {
      if (scanMode === 'upload') {
        setError('No QR code detected in the image. Please ensure:\n• The image contains a clear, readable QR code\n• The QR code is not too small or blurry\n• The image is well-lit and in focus\n• Try a different image or use camera scanning');
        setIsProcessingImage(false);
      }
      // For camera mode, don't show error as it's expected during scanning
    } else if (errorMessage.includes('NotAllowedError') || errorMessage.includes('Permission')) {
      setError('Camera access denied. Please allow camera permissions and try again.');
    } else if (errorMessage.includes('NotFoundError') || errorMessage.includes('No camera')) {
      setError('No camera found. Please use image upload instead.');
    } else {
      // For other errors, show a generic message
      if (scanMode === 'upload') {
        setError('Could not process the image. Please try a different image or use camera scanning.');
        setIsProcessingImage(false);
      }
    }
  };

  const startCameraScanner = () => {
    if (!containerRef.current) return;

    try {
      scannerRef.current = new Html5QrcodeScanner(
        "qr-reader",
        {
          fps: 10,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0,
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
          // Additional configuration for better performance
          disableFlip: false
          // Removed cameraId: undefined, as it is not a valid property
        },
        false
      );

      scannerRef.current.render(handleScanSuccess, handleScanError);
      setIsScanning(true);
      setError('');
    } catch (err) {
      console.error('Failed to start camera scanner:', err);
      setError('Failed to start camera. Please check camera permissions or try image upload instead.');
    }
  };

  const scanImageFile = async (file: File) => {
    try {
      setIsProcessingImage(true);
      setError('');

      // Additional validation
      if (!file) {
        throw new Error('No file selected');
      }

      // Check if file is actually an image
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        throw new Error('Selected file is not an image');
      }

      // Check for supported formats
      const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/avif'];
      if (!supportedFormats.includes(file.type.toLowerCase())) {
        throw new Error(`Unsupported image format: ${file.type}. Please use JPG, PNG, GIF, WebP, or AVIF.`);
      }

      // Check file size (max 10MB for better quality images)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('Image file is too large. Please select an image smaller than 10MB.');
      }

      // Create a new Html5Qrcode instance for image scanning
      const html5QrCode = new Html5Qrcode("qr-image-reader");
      
      // Configure scanning options for better detection
      const config = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
        disableFlip: false
      };
      
      console.log('Scanning image file:', file.name, 'Type:', file.type, 'Size:', file.size);
      // Html5Qrcode.scanFile expects (file: File, showImage: boolean)
      const result = await html5QrCode.scanFile(file, true);
      handleScanSuccess(result);
    } catch (err) {
      console.error('Failed to scan image:', err);
      
      // Provide specific error messages based on error type
      if (err instanceof Error) {
        if (err.message.includes('NotFoundException') || err.message.includes('No MultiFormat Readers')) {
          setError('No QR code found in the image. Please ensure:\n• The image contains a clear, readable QR code\n• The QR code is not too small or blurry\n• The image is well-lit and in focus\n• The QR code has good contrast (black on white)\n• Try a different image or use camera scanning');
        } else if (err.message.includes('NotAllowedError')) {
          setError('Permission denied. Please check file access permissions.');
        } else if (err.message.includes('NotSupportedError')) {
          setError('File format not supported. Please use JPG, PNG, GIF, or WebP images.');
        } else if (err.message.includes('Unsupported image format')) {
          setError(err.message);
        } else if (err.message.includes('too large')) {
          setError(err.message);
        } else {
          setError(`Scanning failed: ${err.message}`);
        }
      } else {
        setError('Could not process the image. Please try a different image or use camera scanning.');
      }
      setIsProcessingImage(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file.');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image file is too large. Please select an image smaller than 5MB.');
      return;
    }

    scanImageFile(file);
  };

  const stopScanner = () => {
    if (scannerRef.current) {
      scannerRef.current.clear();
      scannerRef.current = null;
    }
    setIsScanning(false);
    setIsProcessingImage(false);
  };

  const handleApply = () => {
    if (scanResult) {
      onScan(scanResult);
      setIsOpen(false);
      setScanResult(null);
    }
  };

  const handleRetry = () => {
    setScanResult(null);
    setError('');
    if (scanMode === 'camera') {
      startCameraScanner();
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      stopScanner();
      setScanResult(null);
      setError('');
      setScanMode('camera');
    }
  };

  const switchToUpload = () => {
    stopScanner();
    setScanMode('upload');
    setError('');
  };

  const switchToCamera = () => {
    stopScanner();
    setScanMode('camera');
    setError('');
    setTimeout(() => {
      startCameraScanner();
    }, 100);
  };

  useEffect(() => {
    if (isOpen && !scannerRef.current && scanMode === 'camera') {
      // Small delay to ensure dialog is fully rendered
      setTimeout(() => {
        startCameraScanner();
      }, 100);
    }

    return () => {
      stopScanner();
    };
  }, [isOpen, scanMode]);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Camera className="h-4 w-4" />
            Scan QR Code
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Scan QR Code
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Mode Selection */}
          {!scanResult && !error && (
            <div className="flex gap-2">
              <Button
                variant={scanMode === 'camera' ? 'default' : 'outline'}
                size="sm"
                onClick={switchToCamera}
                className="flex-1"
              >
                <Camera className="h-4 w-4 mr-2" />
                Camera
              </Button>
              <Button
                variant={scanMode === 'upload' ? 'default' : 'outline'}
                size="sm"
                onClick={switchToUpload}
                className="flex-1"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </Button>
            </div>
          )}

          {error && (
            <div className="space-y-3">
              <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="text-red-700 text-sm">
                  {error.split('\n').map((line, index) => (
                    <div key={index} className={index > 0 ? 'mt-1' : ''}>
                      {line}
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Retry button for upload errors */}
              {scanMode === 'upload' && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setError('');
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                    className="flex-1"
                  >
                    Try Different Image
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={switchToCamera}
                    className="flex-1"
                  >
                    Use Camera Instead
                  </Button>
                </div>
              )}
            </div>
          )}

          {!scanResult && !error && scanMode === 'camera' && (
            <div className="space-y-4">
              <div className="text-center text-sm text-gray-600">
                Point your camera at a QR code to scan
              </div>
              
              {/* Camera scanning tips */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-medium text-blue-900 mb-2 text-sm">Camera scanning tips:</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Hold your device steady and close to the QR code</li>
                  <li>• Ensure good lighting and avoid shadows</li>
                  <li>• Make sure the QR code is fully visible in the frame</li>
                  <li>• Try different angles if scanning doesn't work immediately</li>
                </ul>
              </div>
              
              <div 
                ref={containerRef}
                id="qr-reader"
                className="w-full"
              />
              {isScanning && (
                <div className="text-center text-sm text-blue-600">
                  Scanning...
                </div>
              )}
            </div>
          )}

          {!scanResult && !error && scanMode === 'upload' && (
            <div className="space-y-4">
              <div className="text-center text-sm text-gray-600">
                Upload an image containing a QR code
              </div>
              
              {/* Tips for better scanning */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-medium text-blue-900 mb-2 text-sm">Tips for better scanning:</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Ensure the QR code is clearly visible and not blurry</li>
                  <li>• Make sure the image is well-lit and in focus</li>
                  <li>• The QR code should be at least 100x100 pixels</li>
                  <li>• Avoid images with too much background clutter</li>
                  <li>• Use images with high contrast (black QR code on white background)</li>
                  <li>• Avoid screenshots of screens (use direct photos instead)</li>
                  <li>• Supported formats: JPG, PNG, GIF, WebP</li>
                </ul>
              </div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <FileImage className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  disabled={isProcessingImage}
                >
                  {isProcessingImage ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Choose Image
                    </>
                  )}
                </Button>
                <p className="text-xs text-gray-500 mt-2">
                  Supported formats: JPG, PNG, GIF, WebP (max 10MB)
                </p>
                <p className="text-xs text-gray-500">
                  For best results, use high-quality images with good contrast
                </p>
              </div>
              {/* Hidden element for image scanning */}
              <div id="qr-image-reader" className="hidden"></div>
            </div>
          )}

          {scanResult && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-green-700 font-medium">QR Code Scanned Successfully!</span>
              </div>

              <div className="space-y-2">
                {scanResult.referralCode && (
                  <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">Referral Code:</span>
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                      {scanResult.referralCode}
                    </code>
                  </div>
                )}
                {scanResult.discountCode && (
                  <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">Discount Code:</span>
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                      {scanResult.discountCode}
                    </code>
                  </div>
                )}
                {!scanResult.referralCode && !scanResult.discountCode && (
                  <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="text-yellow-700 text-sm">
                      QR code scanned but no referral or discount codes found. This might not be a valid partner QR code.
                    </span>
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={handleApply} 
                  className="flex-1"
                  disabled={!scanResult.referralCode && !scanResult.discountCode}
                >
                  Apply Codes
                </Button>
                <Button variant="outline" onClick={handleRetry}>
                  Scan Again
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 