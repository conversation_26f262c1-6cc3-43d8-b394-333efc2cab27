"use client";

import { useState, useEffect } from "react";
import { Product } from "@/utils/types";
import { generateRecommendations, RecommendationContext } from "@/utils/smart-recommendations";
import ProductCard from "./product-card";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Sparkles, TrendingUp, Star, Tag } from "lucide-react";
import { cn } from "@/lib/utils";

interface SmartRecommendationsProps {
  currentProduct?: Product;
  allProducts: Product[];
  className?: string;
  maxRecommendations?: number;
}

export default function SmartRecommendations({
  currentProduct,
  allProducts,
  className,
  maxRecommendations = 8
}: SmartRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [currentSection, setCurrentSection] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    generateSmartRecommendations();
  }, [currentProduct, allProducts]);

  const generateSmartRecommendations = async () => {
    setIsLoading(true);
    
    try {
      // Get user behavior from localStorage
      const userBehavior = {
        viewedProducts: JSON.parse(localStorage.getItem('viewedProducts') || '[]'),
        searchHistory: JSON.parse(localStorage.getItem('searchHistory') || '[]'),
        cartItems: JSON.parse(localStorage.getItem('cartItems') || '[]'),
        favoriteProducts: JSON.parse(localStorage.getItem('favoriteProducts') || '[]')
      };

      // Get user preferences from localStorage
      const userPreferences = {
        preferredBrands: JSON.parse(localStorage.getItem('preferredBrands') || '[]'),
        preferredPriceRange: JSON.parse(localStorage.getItem('preferredPriceRange') || '[0, 2000]'),
        preferredSizes: JSON.parse(localStorage.getItem('preferredSizes') || '[]'),
        preferredColors: JSON.parse(localStorage.getItem('preferredColors') || '[]')
      };

      const context: RecommendationContext = {
        currentProduct,
        userBehavior,
        userPreferences
      };

      const recs = generateRecommendations(allProducts, context, maxRecommendations);
      setRecommendations(recs);
    } catch (error) {
      console.error('Error generating recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRecommendationIcon = (reason: string) => {
    if (reason.includes('brand') || reason.includes('Brand')) return <Sparkles className="h-4 w-4" />;
    if (reason.includes('Trending') || reason.includes('Popular')) return <TrendingUp className="h-4 w-4" />;
    if (reason.includes('rating') || reason.includes('Rated')) return <Star className="h-4 w-4" />;
    if (reason.includes('offer') || reason.includes('Sale')) return <Tag className="h-4 w-4" />;
    return <Sparkles className="h-4 w-4" />;
  };

  const nextSection = () => {
    setCurrentSection((prev) => (prev + 1) % recommendations.length);
  };

  const prevSection = () => {
    setCurrentSection((prev) => (prev - 1 + recommendations.length) % recommendations.length);
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-600" />
            Smart Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (recommendations.length === 0) {
    return null;
  }

  const currentRecommendation = recommendations[currentSection];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {getRecommendationIcon(currentRecommendation.reason)}
            {currentRecommendation.reason}
          </CardTitle>
          
          {recommendations.length > 1 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={prevSection}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="flex gap-1">
                {recommendations.map((_, index) => (
                  <button
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors",
                      index === currentSection ? "bg-blue-600" : "bg-gray-300"
                    )}
                    onClick={() => setCurrentSection(index)}
                  />
                ))}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={nextSection}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>{currentRecommendation.confidence}% match</span>
          </div>
          <span>•</span>
          <span>{currentRecommendation.products.length} products</span>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {currentRecommendation.products.map((product: Product) => (
            <div key={product.id} className="transform transition-transform hover:scale-105">
              <ProductCard
                product={product}
                onQuickView={() => {
                  // Track product view for future recommendations
                  const viewedProducts = JSON.parse(localStorage.getItem('viewedProducts') || '[]');
                  const updatedViewed = [product.id, ...viewedProducts.filter((id: string) => id !== product.id)].slice(0, 20);
                  localStorage.setItem('viewedProducts', JSON.stringify(updatedViewed));
                }}
              />
            </div>
          ))}
        </div>
        
        {currentRecommendation.products.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Sparkles className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No recommendations available at the moment.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Hook to track user behavior for recommendations
 */
export function useRecommendationTracking() {
  const trackProductView = (productId: string) => {
    try {
      const viewedProducts = JSON.parse(localStorage.getItem('viewedProducts') || '[]');
      const updatedViewed = [productId, ...viewedProducts.filter((id: string) => id !== productId)].slice(0, 20);
      localStorage.setItem('viewedProducts', JSON.stringify(updatedViewed));
    } catch (error) {
      console.error('Error tracking product view:', error);
    }
  };

  const trackSearch = (query: string) => {
    try {
      const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      const updatedHistory = [query, ...searchHistory.filter((q: string) => q !== query)].slice(0, 10);
      localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error tracking search:', error);
    }
  };

  const trackBrandPreference = (brand: string) => {
    try {
      const preferredBrands = JSON.parse(localStorage.getItem('preferredBrands') || '[]');
      const updatedBrands = [brand, ...preferredBrands.filter((b: string) => b !== brand)].slice(0, 5);
      localStorage.setItem('preferredBrands', JSON.stringify(updatedBrands));
    } catch (error) {
      console.error('Error tracking brand preference:', error);
    }
  };

  const trackPriceRange = (minPrice: number, maxPrice: number) => {
    try {
      localStorage.setItem('preferredPriceRange', JSON.stringify([minPrice, maxPrice]));
    } catch (error) {
      console.error('Error tracking price range:', error);
    }
  };

  return {
    trackProductView,
    trackSearch,
    trackBrandPreference,
    trackPriceRange
  };
}
