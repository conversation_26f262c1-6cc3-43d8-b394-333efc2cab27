"use client";

import { useEffect, useState } from "react";
import AdminLayout from "@/components/admin/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw,
  ArrowRight,
  Eye,
  Save
} from "lucide-react";
import { toast } from "react-hot-toast";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

interface CategoryIssue {
  id: string;
  name: string;
  currentCategory: string;
  currentCategoryId: string;
  suggestedCategory: string;
  confidence: number;
  brand: string;
  price: number;
  images: string[];
}

interface Category {
  id: string;
  name: string;
  description?: string;
  _count: { products: number };
}

export default function CategoryManagementPage() {
  const [issues, setIssues] = useState<CategoryIssue[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedIssues, setSelectedIssues] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch category issues
  const fetchIssues = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/products/category-issues');
      const result = await response.json();
      
      if (result.success) {
        setIssues(result.data);
      } else {
        setError(result.error || 'Failed to fetch category issues');
      }
    } catch (err) {
      setError('Failed to fetch category issues');
      console.error('Error fetching issues:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories');
      const result = await response.json();
      
      if (result.success) {
        setCategories(result.data);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  // Handle bulk update
  const handleBulkUpdate = async () => {
    if (selectedIssues.size === 0) {
      toast.error('Please select at least one product to update');
      return;
    }

    try {
      setUpdating(true);
      
      const updates = Array.from(selectedIssues).map(issueId => {
        const issue = issues.find(i => i.id === issueId);
        const targetCategory = categories.find(c => c.name === issue?.suggestedCategory);
        
        return {
          productId: issueId,
          newCategoryId: targetCategory?.id,
          reason: 'Bulk category correction based on product name/description analysis'
        };
      }).filter(update => update.newCategoryId);

      const response = await fetch('/api/admin/products/bulk-category-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates }),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Successfully updated ${updates.length} products`);
        setSelectedIssues(new Set());
        await fetchIssues(); // Refresh the list
      } else {
        toast.error(result.error || 'Failed to update products');
      }
    } catch (err) {
      toast.error('Failed to update products');
      console.error('Error updating products:', err);
    } finally {
      setUpdating(false);
    }
  };

  // Handle individual category change
  const handleIndividualUpdate = async (productId: string, newCategoryId: string) => {
    try {
      const updates = [{
        productId,
        newCategoryId,
        reason: 'Manual category correction'
      }];

      const response = await fetch('/api/admin/products/bulk-category-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates }),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('Product category updated successfully');
        await fetchIssues(); // Refresh the list
      } else {
        toast.error(result.error || 'Failed to update product');
      }
    } catch (err) {
      toast.error('Failed to update product');
      console.error('Error updating product:', err);
    }
  };

  // Toggle issue selection
  const toggleIssueSelection = (issueId: string) => {
    const newSelected = new Set(selectedIssues);
    if (newSelected.has(issueId)) {
      newSelected.delete(issueId);
    } else {
      newSelected.add(issueId);
    }
    setSelectedIssues(newSelected);
  };

  // Select all issues
  const selectAllIssues = () => {
    setSelectedIssues(new Set(issues.map(i => i.id)));
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedIssues(new Set());
  };

  useEffect(() => {
    fetchIssues();
    fetchCategories();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout
      user={{
        id: "admin",
        name: "Admin User",
        email: "<EMAIL>",
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
        role: "ADMIN" as any,
      }}
    >
      <div className="max-w-7xl mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Category Management</h1>
            <p className="text-gray-600 mt-2">
              Manage product categories and fix misplaced products
            </p>
          </div>
          <Button onClick={fetchIssues} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {error && (
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="issues" className="space-y-6">
          <TabsList>
            <TabsTrigger value="issues">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Category Issues ({issues.length})
            </TabsTrigger>
            <TabsTrigger value="overview">
              <Package className="h-4 w-4 mr-2" />
              Category Overview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="issues" className="space-y-6">
            {issues.length > 0 && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Bulk Actions</CardTitle>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={selectAllIssues}
                      >
                        Select All
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={clearSelection}
                      >
                        Clear Selection
                      </Button>
                      <Button 
                        onClick={handleBulkUpdate}
                        disabled={selectedIssues.size === 0 || updating}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {updating ? (
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        Update Selected ({selectedIssues.size})
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            )}

            <div className="grid gap-4">
              {issues.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Category Issues Found
                    </h3>
                    <p className="text-gray-500">
                      All products appear to be in the correct categories.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                issues.map((issue) => (
                  <Card key={issue.id} className="overflow-hidden">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <Checkbox
                          checked={selectedIssues.has(issue.id)}
                          onCheckedChange={() => toggleIssueSelection(issue.id)}
                        />
                        
                        {issue.images[0] && (
                          <img
                            src={issue.images[0]}
                            alt={issue.name}
                            className="w-16 h-16 object-cover rounded-lg"
                          />
                        )}
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-medium text-gray-900 truncate">
                                {issue.name}
                              </h3>
                              <p className="text-sm text-gray-500">
                                {issue.brand} • M{issue.price}
                              </p>
                            </div>
                            <Badge variant="secondary">
                              Confidence: {issue.confidence}
                            </Badge>
                          </div>
                          
                          <div className="mt-4 flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                Current: {issue.currentCategory}
                              </Badge>
                              <ArrowRight className="h-4 w-4 text-gray-400" />
                              <Badge className="bg-green-100 text-green-800">
                                Suggested: {issue.suggestedCategory}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-2 ml-auto">
                              <Select
                                onValueChange={(value) => 
                                  handleIndividualUpdate(issue.id, value)
                                }
                              >
                                <SelectTrigger className="w-40">
                                  <SelectValue placeholder="Move to..." />
                                </SelectTrigger>
                                <SelectContent>
                                  {categories.map((category) => (
                                    <SelectItem 
                                      key={category.id} 
                                      value={category.id}
                                    >
                                      {category.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category) => (
                <Card key={category.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {category.name}
                      <Badge variant="secondary">
                        {category._count.products} products
                      </Badge>
                    </CardTitle>
                    {category.description && (
                      <p className="text-sm text-gray-600">
                        {category.description}
                      </p>
                    )}
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
