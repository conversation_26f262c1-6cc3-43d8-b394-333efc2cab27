import { NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// POST /api/admin/products/[id]/move-category - Move a single product to a different category
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await context.params;
    await requireAdmin();

    const body = await request.json();
    const { categoryId, reason } = body;

    // Validate input
    if (!categoryId) {
      return NextResponse.json(
        { success: false, error: "Category ID is required" },
        { status: 400 }
      );
    }

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: { category: true },
    });

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Verify category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Category not found" },
        { status: 400 }
      );
    }

    // Check if product is already in this category
    if (product.categoryId === categoryId) {
      return NextResponse.json(
        { success: false, error: "Product is already in this category" },
        { status: 400 }
      );
    }

    // Update product category
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: { categoryId },
      include: { category: true },
    });

    // Log the change
    console.log(`Product "${product.name}" moved from "${product.category.name}" to "${category.name}"${reason ? ` - Reason: ${reason}` : ''}`);

    const response: ApiResponse<typeof updatedProduct> = {
      success: true,
      data: updatedProduct,
      message: `Product moved to ${category.name} successfully`,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error moving product category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to move product category" },
      { status: 500 }
    );
  }
}
